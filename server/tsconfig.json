{
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig to read more about this file */

    /* Language and Environment */
    "target": "ES2017",                                  /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */

    /* Modules */
    "module": "Node16",                                /* Specify what module code is generated. */
    "rootDir": "./src",                                  /* Specify the root folder within your source files. */
    "baseUrl": "./",                                     /* Specify the base directory to resolve non-relative module names. */
    "paths": {                                           /* Specify a set of entries that re-map imports to additional lookup locations. */
      "*": ["node_modules/*"]
    },
    "moduleResolution": "Node16",                         /* Specify how TypeScript looks up a file from a given module specifier. */

    /* Emit */
    "outDir": "./dist",                                  /* Specify an output folder for all emitted files. */
    "sourceMap": true,                                   /* Create source map files for emitted JavaScript files. */

    /* Interop Constraints */
    "esModuleInterop": true,                             /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */
    "forceConsistentCasingInFileNames": true,            /* Ensure that casing is correct in imports. */

    /* Type Checking */
    "strict": false,                                      /* Enable all strict type-checking options. */

    /* Completeness */
    "skipLibCheck": true,                                /* Skip type checking all .d.ts files. */

    /* Additional Features */
    "resolveJsonModule": true,                            /* Enable importing .json files. */

    /* Ignore unknown and any type */
    "noImplicitAny": false,
    "strictNullChecks": false
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "**/*.test.ts"]
}
