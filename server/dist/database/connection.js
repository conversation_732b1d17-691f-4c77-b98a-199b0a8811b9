"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectDB = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const env_1 = require("../config/env");
const { mongo } = env_1.config;
/**
 * <PERSON>les connecting to MongoDB with retry logic and proper error handling
 */
const connectDB = async () => {
    try {
        const conn = await mongoose_1.default.connect(mongo.uri, mongo.options);
        console.log(`MongoDB connected: ${conn.connection.host}`);
        // Handle connection events
        mongoose_1.default.connection.on('error', err => {
            console.error(`MongoDB connection error: ${err}`);
        });
        mongoose_1.default.connection.on('disconnected', () => {
            console.log('MongoDB disconnected');
        });
        // Handle process termination
        process.on('SIGINT', async () => {
            await mongoose_1.default.connection.close();
            console.log('MongoDB connection closed due to app termination');
            process.exit(0);
        });
    }
    catch (error) {
        console.error(`Error connecting to MongoDB: ${error.message}`);
        // Retry connection after 5 seconds
        console.log('Retrying connection in 5 seconds...');
        setTimeout(exports.connectDB, 5000);
    }
};
exports.connectDB = connectDB;
exports.default = exports.connectDB;
//# sourceMappingURL=connection.js.map