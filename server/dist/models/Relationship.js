"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const RelationshipSchema = new mongoose_1.Schema({
    userOne: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    userTwo: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    status: {
        type: String,
        enum: ['pending', 'active', 'blocked', 'declined'],
        default: 'pending',
    },
    activatedAt: {
        type: Date,
    },
    lastInteractionDate: {
        type: Date,
    },
    askedQuestions: [{
            questionId: {
                type: mongoose_1.Schema.Types.ObjectId,
                ref: 'Question',
                required: true,
            },
            dateAssigned: {
                type: Date,
                default: new Date().toISOString().split('T')[0],
            },
            answers: [{
                    userId: {
                        type: mongoose_1.Schema.Types.ObjectId,
                        ref: 'User',
                        required: true,
                    },
                    content: {
                        type: String,
                    },
                    submittedAt: {
                        type: Date,
                        default: Date.now,
                    },
                }],
        }],
    alreadyAskedQuestions: [{
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Question',
        }],
    metadata: {
        initiator: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        questionStreak: {
            type: Number,
            default: 0,
        },
        totalInteractions: {
            type: Number,
            default: 0,
        },
        compatibilityScore: {
            type: Number,
            min: 0,
            max: 100,
        },
        notes: {
            type: String,
        },
        timezone: {
            type: String,
            default: "Asia/Kolkata" // default timezone
        },
        preferredQuestionTime: {
            type: String,
            default: "08:00" // default to 8:00 AM
        }
    },
}, {
    timestamps: true,
});
// Ensure each relationship between two users is unique
RelationshipSchema.index({
    userOne: 1,
    userTwo: 1
}, {
    unique: true
});
// For finding relationships by user
RelationshipSchema.index({ userOne: 1, status: 1 });
RelationshipSchema.index({ userTwo: 1, status: 1 });
// Static method to find the active relationship for a user
RelationshipSchema.statics.findActiveRelationshipForUser = async function (userId) {
    return this.findOne({
        $or: [
            { userOne: userId, status: 'active' },
            { userTwo: userId, status: 'active' }
        ]
    }).populate('userOne userTwo');
};
// Check if a user has any active relationship
RelationshipSchema.statics.hasActiveRelationship = async function (userId) {
    const count = await this.countDocuments({
        $or: [
            { userOne: userId, status: 'active' },
            { userTwo: userId, status: 'active' }
        ]
    });
    return count > 0;
};
// Pre-save middleware to ensure userOne is always lexicographically smaller than userTwo
// This ensures consistent relationship lookup and prevents duplicate relationships
RelationshipSchema.pre('save', async function (next) {
    const userOneOriginal = this.userOne;
    const userTwoOriginal = this.userTwo;
    const userOne = userOneOriginal.toString();
    const userTwo = userTwoOriginal.toString();
    console.log('userOne', userOne, 'userTwo', userTwo);
    if (userOne === userTwo) {
        const err = new Error('Wait! Cannot create relationship with self');
        return next(err);
    }
    // Always store the smaller ID in userOne for consistent querying
    if (userOne > userTwo) {
        this.userOne = userTwoOriginal;
        this.userTwo = userOneOriginal;
    }
    // If status is being changed to active, ensure neither user is already in an active relationship
    if (this.isModified('status') && this.status === 'active') {
        this.activatedAt = new Date();
        // Check if either user already has an active relationship
        const RelationshipModel = mongoose_1.default.model('Relationship');
        const [userOneHasActiveRelation, userTwoHasActiveRelation] = await Promise.all([
            RelationshipModel.countDocuments({
                $or: [
                    { userOne: this.userOne, status: 'active', _id: { $ne: this._id } },
                    { userTwo: this.userOne, status: 'active', _id: { $ne: this._id } }
                ]
            }),
            RelationshipModel.countDocuments({
                $or: [
                    { userOne: this.userTwo, status: 'active', _id: { $ne: this._id } },
                    { userTwo: this.userTwo, status: 'active', _id: { $ne: this._id } }
                ]
            })
        ]);
        if (userOneHasActiveRelation) {
            const err = new Error('User One already has an active relationship');
            return next(err);
        }
        if (userTwoHasActiveRelation) {
            const err = new Error('User Two already has an active relationship');
            return next(err);
        }
    }
    next();
});
exports.default = mongoose_1.default.model('Relationship', RelationshipSchema);
//# sourceMappingURL=Relationship.js.map