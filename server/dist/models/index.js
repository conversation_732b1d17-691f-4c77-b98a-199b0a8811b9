"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Relationship = exports.Question = exports.User = void 0;
const User_1 = __importDefault(require("./User"));
exports.User = User_1.default;
const Question_1 = __importDefault(require("./Question"));
exports.Question = Question_1.default;
const Relationship_1 = __importDefault(require("./Relationship"));
exports.Relationship = Relationship_1.default;
//# sourceMappingURL=index.js.map