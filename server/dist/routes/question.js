"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const questionController_1 = require("../controllers/questionController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// All routes require authentication
router.use(auth_1.auth);
// Get daily question for the current user
router.get('/daily', questionController_1.questionController.getDailyQuestion);
// Submit an answer to the daily question
router.post('/answer', questionController_1.questionController.submitAnswer);
// Get answer history for the user's relationship
router.get('/history', questionController_1.questionController.getAnswerHistory);
exports.default = router;
//# sourceMappingURL=question.js.map