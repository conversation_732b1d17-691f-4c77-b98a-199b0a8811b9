"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const userController_1 = require("../controllers/userController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// Public routes
router.post('/register', userController_1.userController.register);
router.post('/login', userController_1.userController.login);
// Protected routes (require authentication)
router.get('/profile', auth_1.auth, userController_1.userController.getProfile);
router.put('/profile', auth_1.auth, userController_1.userController.updateProfile);
router.put('/privacy-settings', auth_1.auth, userController_1.userController.updatePrivacySettings);
// Relationship routes
router.post('/relationship/request', auth_1.auth, userController_1.userController.sendRelationshipRequest);
router.get('/relationship/pending', auth_1.auth, userController_1.userController.getPendingRelationships);
router.post('/relationship/:relationshipId/accept', auth_1.auth, userController_1.userController.acceptRelationshipRequest);
router.post('/relationship/:relationshipId/decline', auth_1.auth, userController_1.userController.declineRelationshipRequest);
router.delete('/relationship/:relationshipId/cancel', auth_1.auth, userController_1.userController.cancelRelationshipRequest);
router.put('/relationship/timezone', auth_1.auth, userController_1.userController.updateTimezonePreferences);
exports.default = router;
//# sourceMappingURL=user.js.map