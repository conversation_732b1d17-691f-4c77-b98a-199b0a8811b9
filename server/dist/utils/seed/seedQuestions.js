"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const models_1 = require("../../models");
const connection_1 = require("../../database/connection");
/**
 * Seed questions data
 */
const seedQuestions = async () => {
    try {
        // Connect to MongoDB
        await (0, connection_1.connectDB)();
        console.log('Connected to MongoDB');
        console.log('Seeding questions...');
        // Remove existing questions
        await models_1.Question.deleteMany({});
        console.log('Deleted existing questions');
        // Create date references for active dates
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const dayBeforeYesterday = new Date(today);
        dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);
        const threeDaysAgo = new Date(today);
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
        const fourDaysAgo = new Date(today);
        fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
        const fiveDaysAgo = new Date(today);
        fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dayAfterTomorrow = new Date(today);
        dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
        // Define questions grouped by category for better organization
        const lightheartedQuestions = [
            {
                text: 'What small moment brought you joy today?',
                description: 'Share a moment, no matter how small, that made you smile or feel good today.',
                category: 'lighthearted',
                tags: ['daily', 'reflection', 'positivity'],
                activeDate: yesterday,
                isActive: true,
                difficulty: 'easy',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'If we could travel anywhere together right now, where would you choose?',
                description: 'Describe your ideal travel destination and why you\'d like to go there with your partner.',
                category: 'lighthearted',
                tags: ['travel', 'future', 'dreams'],
                activeDate: today,
                isActive: true,
                difficulty: 'easy',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s your favorite meal that we\'ve shared together?',
                description: 'Think about a memorable meal you\'ve had with your partner.',
                category: 'lighthearted',
                tags: ['food', 'memories', 'connection'],
                activeDate: dayBeforeYesterday,
                isActive: true,
                difficulty: 'easy',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What song always makes you think of me?',
                description: 'Share a song that reminds you of your partner and explain why.',
                category: 'lighthearted',
                tags: ['music', 'memories', 'connection'],
                activeDate: threeDaysAgo,
                isActive: true,
                difficulty: 'easy',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s a simple pleasure you think we should enjoy more often?',
                description: 'Suggest something small but meaningful you\'d like to do more frequently together.',
                category: 'lighthearted',
                tags: ['experiences', 'joy', 'togetherness'],
                activeDate: fiveDaysAgo,
                isActive: true,
                difficulty: 'easy',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'If we had a free day together with no obligations, how would you want to spend it?',
                description: 'Describe your ideal day together with no responsibilities or time constraints.',
                category: 'lighthearted',
                tags: ['free time', 'wishes', 'connection'],
                activeDate: tomorrow,
                isActive: true,
                difficulty: 'easy',
                visibilitySettings: { isPublic: true }
            }
        ];
        const thoughtfulQuestions = [
            {
                text: 'What is something you\'re proud of that you rarely talk about?',
                description: 'Share an achievement or quality that you value in yourself but don\'t often mention.',
                category: 'thoughtful',
                tags: ['vulnerability', 'personal', 'growth'],
                activeDate: tomorrow,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s one thing that challenged you this week?',
                description: 'Describe a challenge you faced and how you handled it.',
                category: 'thoughtful',
                tags: ['challenge', 'growth', 'reflection'],
                activeDate: dayAfterTomorrow,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'How has your perspective on our relationship changed since we first met?',
                description: 'Reflect on how your understanding of your connection has evolved over time.',
                category: 'thoughtful',
                tags: ['relationship', 'growth', 'reflection'],
                activeDate: fourDaysAgo,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s something you\'ve been wanting to tell me but haven\'t found the right moment for?',
                description: 'Share something that\'s been on your mind that you haven\'t expressed yet.',
                category: 'thoughtful',
                tags: ['communication', 'vulnerability', 'honesty'],
                activeDate: dayBeforeYesterday,
                isActive: true,
                difficulty: 'hard',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'In what ways do you think we balance each other out?',
                description: 'Explore how your differences or similarities create harmony in your relationship.',
                category: 'thoughtful',
                tags: ['compatibility', 'strengths', 'dynamics'],
                activeDate: threeDaysAgo,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            }
        ];
        const personalQuestions = [
            {
                text: 'What\'s something you\'ve been working on improving about yourself recently?',
                description: 'Share a personal growth goal or habit you\'ve been focusing on.',
                category: 'personal',
                tags: ['growth', 'self-improvement', 'goals'],
                activeDate: today,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s a fear you\'ve overcome or are working to overcome?',
                description: 'Discuss a fear or insecurity and your journey with it.',
                category: 'personal',
                tags: ['vulnerability', 'fears', 'growth'],
                activeDate: fiveDaysAgo,
                isActive: true,
                difficulty: 'hard',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'How do you prefer to be supported when you\'re going through a difficult time?',
                description: 'Share specific ways your partner can best support you during challenges.',
                category: 'personal',
                tags: ['support', 'communication', 'needs'],
                activeDate: dayAfterTomorrow,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s a childhood memory that shaped who you are today?',
                description: 'Share a formative experience from your youth and how it influenced you.',
                category: 'personal',
                tags: ['childhood', 'memories', 'influence'],
                activeDate: fourDaysAgo,
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            }
        ];
        const futureOrientedQuestions = [
            {
                text: 'Where do you see us five years from now?',
                description: 'Share your vision for your relationship in the future.',
                category: 'future-oriented',
                tags: ['future', 'relationship', 'goals'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 3),
                isActive: true,
                difficulty: 'hard',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s something you\'d like us to learn or experience together in the future?',
                description: 'Suggest an activity, skill, or experience you want to share with your partner.',
                category: 'future-oriented',
                tags: ['experiences', 'goals', 'growth'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 4),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What aspect of our relationship would you like to strengthen in the coming year?',
                description: 'Identify an area of your relationship you\'d like to focus on improving.',
                category: 'future-oriented',
                tags: ['growth', 'improvement', 'goals'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 5),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s a tradition you\'d like to establish or maintain in our relationship?',
                description: 'Suggest a ritual or tradition that could strengthen your bond.',
                category: 'future-oriented',
                tags: ['traditions', 'connection', 'rituals'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 6),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            }
        ];
        const intimacyQuestions = [
            {
                text: 'What makes you feel most loved and appreciated in our relationship?',
                description: 'Share specific actions or words that make you feel valued and loved.',
                category: 'intimacy',
                tags: ['love languages', 'connection', 'appreciation'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 6),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s something I do that makes you feel especially connected to me?',
                description: 'Identify specific behaviors or moments that strengthen your bond.',
                category: 'intimacy',
                tags: ['connection', 'affection', 'closeness'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'When did you first realize you were falling for me?',
                description: 'Share the moment or realization when your feelings deepened.',
                category: 'intimacy',
                tags: ['memories', 'feelings', 'beginnings'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s something you\'ve learned about yourself through our relationship?',
                description: 'Reflect on how being with your partner has helped you grow or understand yourself better.',
                category: 'intimacy',
                tags: ['growth', 'self-awareness', 'relationship'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 8),
                isActive: true,
                difficulty: 'hard',
                visibilitySettings: { isPublic: true }
            },
            {
                text: 'What\'s your favorite way that we express affection to each other?',
                description: 'Describe the expressions of love that mean the most to you.',
                category: 'intimacy',
                tags: ['affection', 'love languages', 'connection'],
                activeDate: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 9),
                isActive: true,
                difficulty: 'medium',
                visibilitySettings: { isPublic: true }
            }
        ];
        // Combine all question categories
        const questions = [
            ...lightheartedQuestions,
            ...thoughtfulQuestions,
            ...personalQuestions,
            ...futureOrientedQuestions,
            ...intimacyQuestions
        ];
        // Insert questions
        await models_1.Question.insertMany(questions);
        console.log(`${questions.length} questions inserted successfully`);
        process.exit(0);
    }
    catch (error) {
        console.error('Error seeding questions:', error);
        process.exit(1);
    }
};
// Run the seed function
seedQuestions();
//# sourceMappingURL=seedQuestions.js.map