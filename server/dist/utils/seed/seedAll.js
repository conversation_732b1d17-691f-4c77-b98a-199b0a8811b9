"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const child_process_1 = require("child_process");
const path_1 = __importDefault(require("path"));
const runSeedScript = (scriptName) => {
    return new Promise((resolve, reject) => {
        console.log(`\n========== Running ${scriptName} ==========\n`);
        const scriptPath = path_1.default.join(__dirname, `${scriptName}.ts`);
        const child = (0, child_process_1.spawn)('npx', ['ts-node', scriptPath], { stdio: 'inherit' });
        child.on('close', (code) => {
            if (code === 0) {
                console.log(`\n========== ${scriptName} completed successfully ==========\n`);
                resolve();
            }
            else {
                console.error(`\n========== ${scriptName} failed with code ${code} ==========\n`);
                reject(new Error(`Script ${scriptName} exited with code ${code}`));
            }
        });
        child.on('error', (err) => {
            console.error(`Failed to start ${scriptName}: ${err}`);
            reject(err);
        });
    });
};
const seedAll = async () => {
    try {
        // Run seed scripts in correct order
        await runSeedScript('seedQuestions');
        await runSeedScript('seedUsers');
        await runSeedScript('seedRelationships');
        console.log('All seed scripts completed successfully');
        process.exit(0);
    }
    catch (error) {
        console.error(`Seed process failed: ${error.message}`);
        process.exit(1);
    }
};
// Run the seed function
seedAll();
//# sourceMappingURL=seedAll.js.map