"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const models_1 = require("../../models");
const connection_1 = require("../../database/connection");
const crypto_1 = __importDefault(require("crypto"));
/**
 * Hash a password (duplicate of UserService method for seed purposes)
 */
const hashPassword = async (password) => {
    return new Promise((resolve, reject) => {
        const salt = crypto_1.default.randomBytes(16).toString('hex');
        crypto_1.default.scrypt(password, salt, 64, (err, derivedKey) => {
            if (err)
                reject(err);
            resolve(salt + ':' + derivedKey.toString('hex'));
        });
    });
};
/**
 * Seed users data
 */
const seedUsers = async () => {
    try {
        // Connect to MongoDB
        await (0, connection_1.connectDB)();
        console.log('Connected to MongoDB');
        console.log('Seeding users...');
        // Remove existing users
        await models_1.User.deleteMany({});
        console.log('Deleted existing users');
        // Create sample users
        const users = [
            {
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                password: await hashPassword('password123'),
                bio: 'I love hiking and reading books.',
                isActive: true,
                preferences: {
                    emailNotifications: true,
                    privacySettings: {
                        showAnswers: true,
                        showProfile: true
                    }
                }
            },
            {
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
                password: await hashPassword('password123'),
                bio: 'Passionate about cooking and travel.',
                isActive: true,
                preferences: {
                    emailNotifications: true,
                    privacySettings: {
                        showAnswers: true,
                        showProfile: true
                    }
                }
            },
            {
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                password: await hashPassword('password123'),
                bio: 'Tech enthusiast and coffee lover.',
                isActive: true,
                preferences: {
                    emailNotifications: false,
                    privacySettings: {
                        showAnswers: false,
                        showProfile: true
                    }
                }
            },
            {
                firstName: 'Bob',
                lastName: 'Brown',
                email: '<EMAIL>',
                password: await hashPassword('password123'),
                bio: 'Music producer and dog person.',
                isActive: true,
                preferences: {
                    emailNotifications: true,
                    privacySettings: {
                        showAnswers: true,
                        showProfile: false
                    }
                }
            }
        ];
        // Insert users
        await models_1.User.insertMany(users);
        console.log(`${users.length} users inserted successfully`);
        // Log emails and passwords for testing
        console.log('Test user credentials:');
        users.forEach(user => {
            console.log(`Email: ${user.email}, Password: password123`);
        });
        process.exit(0);
    }
    catch (error) {
        console.error('Error seeding users:', error);
        process.exit(1);
    }
};
// Run the seed function
seedUsers();
//# sourceMappingURL=seedUsers.js.map