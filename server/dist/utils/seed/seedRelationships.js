"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const models_1 = require("../../models");
const connection_1 = require("../../database/connection");
/**
 * Seed relationships data
 */
const seedRelationships = async () => {
    try {
        // Connect to MongoDB
        await (0, connection_1.connectDB)();
        console.log('Connected to MongoDB');
        console.log('Seeding relationships...');
        // Remove existing relationships
        await models_1.Relationship.deleteMany({});
        console.log('Deleted existing relationships');
        // Get users from database
        const users = await models_1.User.find();
        if (users.length < 2) {
            console.error('Not enough users to create relationships. Please run seedUsers.ts first.');
            process.exit(1);
        }
        // Get questions from database
        const questions = await models_1.Question.find({ isActive: true });
        if (questions.length === 0) {
            console.error('No active questions found. Please run seedQuestions.ts first.');
            process.exit(1);
        }
        // Create an active relationship between the first two users with extensive history
        const john = users.find(user => user.email === '<EMAIL>');
        const jane = users.find(user => user.email === '<EMAIL>');
        if (john && jane) {
            console.log('Creating active relationship with extensive history between John and Jane...');
            // Create an array of dates for the past 30 days for question history
            const dates = [];
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            for (let i = 0; i < 30; i++) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                dates.push(date);
            }
            // Create an array of asked questions with answers for each date
            const askedQuestions = [];
            // Use available questions and cycle through them if needed
            for (let i = 0; i < dates.length; i++) {
                const questionIndex = i % questions.length;
                const currentQuestion = questions[questionIndex];
                const dateAssigned = dates[i];
                // Random time offset for when questions were answered (1-8 hours after assignment)
                const johnAnswerTime = new Date(dateAssigned.getTime() + (1 + Math.floor(Math.random() * 4)) * 60 * 60 * 1000);
                const janeAnswerTime = new Date(dateAssigned.getTime() + (5 + Math.floor(Math.random() * 4)) * 60 * 60 * 1000);
                // Generate answer content based on the question category
                let johnAnswer = '';
                let janeAnswer = '';
                switch (currentQuestion.category) {
                    case 'lighthearted':
                        johnAnswer = getSampleAnswer('lighthearted', 'john');
                        janeAnswer = getSampleAnswer('lighthearted', 'jane');
                        break;
                    case 'thoughtful':
                        johnAnswer = getSampleAnswer('thoughtful', 'john');
                        janeAnswer = getSampleAnswer('thoughtful', 'jane');
                        break;
                    case 'personal':
                        johnAnswer = getSampleAnswer('personal', 'john');
                        janeAnswer = getSampleAnswer('personal', 'jane');
                        break;
                    case 'future-oriented':
                        johnAnswer = getSampleAnswer('future-oriented', 'john');
                        janeAnswer = getSampleAnswer('future-oriented', 'jane');
                        break;
                    case 'intimacy':
                        johnAnswer = getSampleAnswer('intimacy', 'john');
                        janeAnswer = getSampleAnswer('intimacy', 'jane');
                        break;
                    default:
                        johnAnswer = "This is my answer to the question.";
                        janeAnswer = "Here's what I think about this question.";
                }
                askedQuestions.push({
                    questionId: currentQuestion._id,
                    dateAssigned: dateAssigned,
                    answers: [
                        {
                            userId: john._id,
                            content: johnAnswer,
                            submittedAt: johnAnswerTime
                        },
                        {
                            userId: jane._id,
                            content: janeAnswer,
                            submittedAt: janeAnswerTime
                        }
                    ]
                });
            }
            const relationship1 = new models_1.Relationship({
                userOne: john._id < jane._id ? john._id : jane._id,
                userTwo: john._id < jane._id ? jane._id : john._id,
                status: 'active',
                activatedAt: new Date(dates[dates.length - 1]), // Relationship started 30 days ago
                lastInteractionDate: new Date(), // Last interaction today
                askedQuestions: askedQuestions,
                alreadyAskedQuestions: askedQuestions.map(q => q.questionId),
                metadata: {
                    initiator: john._id,
                    questionStreak: 30, // They've answered questions every day for 30 days
                    totalInteractions: 60 + Math.floor(Math.random() * 20), // Some additional random interactions
                    compatibilityScore: 85,
                    notes: 'Strong compatibility across multiple interests'
                }
            });
            await relationship1.save();
            console.log(`Created active relationship between ${john.firstName} and ${jane.firstName} with ${askedQuestions.length} asked questions`);
        }
        // Create a pending relationship between the other two users
        const alice = users.find(user => user.email === '<EMAIL>');
        const bob = users.find(user => user.email === '<EMAIL>');
        if (alice && bob) {
            const relationship2 = new models_1.Relationship({
                userOne: alice._id < bob._id ? alice._id : bob._id,
                userTwo: alice._id < bob._id ? bob._id : alice._id,
                status: 'pending',
                askedQuestions: [], // No questions for pending relationships
                alreadyAskedQuestions: [],
                metadata: {
                    initiator: alice._id,
                    notes: 'Connected through mutual friend'
                }
            });
            await relationship2.save();
            console.log(`Created pending relationship between ${alice.firstName} and ${bob.firstName}`);
        }
        console.log('Relationships seeded successfully');
        process.exit(0);
    }
    catch (error) {
        console.error('Error seeding relationships:', error);
        process.exit(1);
    }
};
/**
 * Helper function to get sample answers based on category and user
 */
function getSampleAnswer(category, user) {
    var _a;
    const answers = {
        lighthearted: {
            john: [
                "Seeing a dog in the park that looked just like my childhood pet.",
                "I'd love to visit Japan with you, especially during cherry blossom season.",
                "That Italian restaurant we went to for our six-month anniversary.",
                "Anything by The Beatles, especially 'Here Comes the Sun'.",
                "I think we should have more picnics in the park when the weather is nice."
            ],
            jane: [
                "When you texted me that silly meme in the middle of my stressful meeting.",
                "Definitely Greece - beautiful islands, amazing food, and rich history.",
                "Our impromptu taco night when we made everything from scratch.",
                "'Our Song' by Taylor Swift always makes me think of our early days.",
                "I'd love for us to stargaze more often, just lying outside talking."
            ]
        },
        thoughtful: {
            john: [
                "I'm actually really proud of how I helped my nephew learn to read last summer.",
                "Managing the project deadline at work while also being present for you during your family stuff.",
                "I've grown to appreciate how communication is the foundation of everything we build together.",
                "I've been wanting to tell you that I'd like to take some classes to advance my career.",
                "Your optimism balances my tendency to worry, and my planning complements your spontaneity."
            ],
            jane: [
                "I rarely mention the volunteer work I do with the literacy program because I don't do it for recognition.",
                "Finding balance between my ambitions at work and making time for self-care has been challenging.",
                "I used to think relationships were about big romantic gestures, now I see it's about choosing each other daily.",
                "I've been wanting to tell you that I sometimes feel insecure about my career path compared to my friends.",
                "You help me be more adventurous, and I think I help you reflect more on your experiences."
            ]
        },
        personal: {
            john: [
                "I've been working on being more present during conversations instead of thinking about what to say next.",
                "I'm still working on my fear of public speaking, but I've made progress by volunteering to lead meetings.",
                "I appreciate direct communication and specific suggestions more than general comfort.",
                "When my parents divorced when I was 9, I became very independent. That's shaped how I handle challenges."
            ],
            jane: [
                "I've been focusing on creating better boundaries at work so I can be more present when I'm with you.",
                "My fear of failure has held me back, but I'm learning to see mistakes as learning opportunities.",
                "I feel most supported when you listen without immediately trying to solve my problems.",
                "Moving around a lot as a military kid taught me to adapt quickly but also made me crave stability."
            ]
        },
        'future-oriented': {
            john: [
                "I see us having our own home, advancing in our careers, and making time for travel adventures.",
                "I'd love for us to learn to cook international cuisines together, starting with Thai food.",
                "I think we could work on being more vulnerable with each other when we're struggling.",
                "I'd like us to establish a tradition of unplugged weekends once a month to really connect."
            ],
            jane: [
                "I imagine us finding a balance between our careers and personal life, maybe starting a family if we decide that's right for us.",
                "I've always wanted to learn photography with someone special, to document our adventures together.",
                "I'd like us to get better at resolving disagreements constructively without letting them linger.",
                "I'd love to start a tradition of anniversary trips to new places that are meaningful to us."
            ]
        },
        intimacy: {
            john: [
                "I feel most loved when you notice small details about my day or remember things that are important to me.",
                "When you ask about my thoughts on important decisions, it makes me feel valued and respected.",
                "It was during our road trip when your playlist had all my favorite songs. I realized how well you knew me.",
                "I've learned that I need occasional alone time to recharge, which isn't something I understood before.",
                "I love when we cook together and have those deep conversations without any distractions."
            ],
            jane: [
                "The small, thoughtful notes you leave for me make me feel incredibly loved and appreciated.",
                "When you put your arm around me in social situations - it feels protective without being possessive.",
                "It was when you remembered exactly how I like my coffee and brought it to me one morning without asking.",
                "This relationship has taught me that I'm stronger than I thought when it comes to being vulnerable.",
                "I love our inside jokes and the little language we've developed that only we understand."
            ]
        }
    };
    // Get a random answer from the appropriate category and user
    const categoryAnswers = ((_a = answers[category]) === null || _a === void 0 ? void 0 : _a[user]) || ["This is my answer to the question."];
    return categoryAnswers[Math.floor(Math.random() * categoryAnswers.length)];
}
// Run the seed function
seedRelationships();
//# sourceMappingURL=seedRelationships.js.map