"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.questionRepository = void 0;
const uuid_1 = require("uuid");
// Mock questions data
const questions = [
    {
        id: (0, uuid_1.v4)(),
        text: 'What small moment brought you joy today?',
        category: 'lighthearted',
        inputType: 'text',
        maxLength: 500,
        createdAt: new Date('2023-01-01'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'If we could travel anywhere together right now, where would you choose?',
        category: 'lighthearted',
        inputType: 'text',
        maxLength: 500,
        createdAt: new Date('2023-01-02'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'How would you rate your day today?',
        category: 'personal',
        inputType: 'rating',
        createdAt: new Date('2023-01-03'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'What is something you\'re proud of that you rarely talk about?',
        category: 'thoughtful',
        inputType: 'text',
        maxLength: 1000,
        createdAt: new Date('2023-01-04'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'Which of these activities would you most enjoy doing together this weekend?',
        category: 'future-oriented',
        inputType: 'multiple-choice',
        options: ['Hiking outdoors', 'Watching a movie', 'Trying a new restaurant', 'Game night at home'],
        createdAt: new Date('2023-01-05'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'What\'s one thing that challenged you this week?',
        category: 'personal',
        inputType: 'text',
        maxLength: 800,
        createdAt: new Date('2023-01-06'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'If you could have dinner with any historical figure, who would it be and why?',
        category: 'thoughtful',
        inputType: 'text',
        maxLength: 800,
        createdAt: new Date('2023-01-07'),
        active: true
    },
    {
        id: (0, uuid_1.v4)(),
        text: 'Where do you see us five years from now?',
        category: 'future-oriented',
        inputType: 'text',
        maxLength: 1000,
        createdAt: new Date('2023-01-08'),
        active: true
    }
];
// Simple question repository with basic operations
exports.questionRepository = {
    // Get all active questions
    getActive: () => questions.filter(q => q.active),
    // Get all questions
    getAll: () => questions,
    // Get question by ID
    getById: (id) => questions.find(q => q.id === id),
    // Get questions by category
    getByCategory: (category) => questions.filter(q => q.category === category && q.active),
    // Create a question
    create: (questionData) => {
        const newQuestion = Object.assign(Object.assign({ id: (0, uuid_1.v4)() }, questionData), { createdAt: new Date(), active: true });
        questions.push(newQuestion);
        return newQuestion;
    },
    // Update a question
    update: (id, questionData) => {
        const questionIndex = questions.findIndex(q => q.id === id);
        if (questionIndex === -1)
            return null;
        questions[questionIndex] = Object.assign(Object.assign({}, questions[questionIndex]), questionData);
        return questions[questionIndex];
    },
    // Deactivate a question (soft delete)
    deactivate: (id) => {
        const questionIndex = questions.findIndex(q => q.id === id);
        if (questionIndex === -1)
            return false;
        questions[questionIndex].active = false;
        return true;
    },
    // Get a random question that hasn't been assigned to a relationship
    getRandomUnassignedQuestion: (assignedQuestionIds) => {
        const availableQuestions = questions.filter(q => q.active && !assignedQuestionIds.includes(q.id));
        // If all questions have been assigned, return a random active question
        if (availableQuestions.length === 0) {
            const activeQuestions = questions.filter(q => q.active);
            return activeQuestions[Math.floor(Math.random() * activeQuestions.length)];
        }
        // Return a random unassigned question
        return availableQuestions[Math.floor(Math.random() * availableQuestions.length)];
    }
};
//# sourceMappingURL=questions.js.map