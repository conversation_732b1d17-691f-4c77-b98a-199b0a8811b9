{"version": 3, "file": "questions.js", "sourceRoot": "", "sources": ["../../../src/utils/mock/questions.ts"], "names": [], "mappings": ";;;AACA,+BAAoC;AAEpC,sBAAsB;AACtB,MAAM,SAAS,GAAe;IAC5B;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,0CAA0C;QAChD,QAAQ,EAAE,cAAc;QACxB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,GAAG;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,yEAAyE;QAC/E,QAAQ,EAAE,cAAc;QACxB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,GAAG;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,oCAAoC;QAC1C,QAAQ,EAAE,UAAU;QACpB,SAAS,EAAE,QAAQ;QACnB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,gEAAgE;QACtE,QAAQ,EAAE,YAAY;QACtB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,6EAA6E;QACnF,QAAQ,EAAE,iBAAiB;QAC3B,SAAS,EAAE,iBAAiB;QAC5B,OAAO,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,oBAAoB,CAAC;QACjG,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,kDAAkD;QACxD,QAAQ,EAAE,UAAU;QACpB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,GAAG;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,+EAA+E;QACrF,QAAQ,EAAE,YAAY;QACtB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,GAAG;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;IACD;QACE,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI,EAAE,0CAA0C;QAChD,QAAQ,EAAE,iBAAiB;QAC3B,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI;KACb;CACF,CAAC;AAEF,mDAAmD;AACtC,QAAA,kBAAkB,GAAG;IAChC,2BAA2B;IAC3B,SAAS,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;IAEhD,oBAAoB;IACpB,MAAM,EAAE,GAAG,EAAE,CAAC,SAAS;IAEvB,qBAAqB;IACrB,OAAO,EAAE,CAAC,EAAU,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;IAEzD,4BAA4B;IAC5B,aAAa,EAAE,CAAC,QAA8B,EAAE,EAAE,CAChD,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC;IAE5D,oBAAoB;IACpB,MAAM,EAAE,CAAC,YAA2D,EAAE,EAAE;QACtE,MAAM,WAAW,iCACf,EAAE,EAAE,IAAA,SAAM,GAAE,IACT,YAAY,KACf,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,MAAM,EAAE,IAAI,GACb,CAAC;QACF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,oBAAoB;IACpB,MAAM,EAAE,CAAC,EAAU,EAAE,YAA2C,EAAE,EAAE;QAClE,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC5D,IAAI,aAAa,KAAK,CAAC,CAAC;YAAE,OAAO,IAAI,CAAC;QAEtC,SAAS,CAAC,aAAa,CAAC,mCAAQ,SAAS,CAAC,aAAa,CAAC,GAAK,YAAY,CAAE,CAAC;QAC5E,OAAO,SAAS,CAAC,aAAa,CAAC,CAAC;IAClC,CAAC;IAED,sCAAsC;IACtC,UAAU,EAAE,CAAC,EAAU,EAAE,EAAE;QACzB,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC5D,IAAI,aAAa,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAEvC,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oEAAoE;IACpE,2BAA2B,EAAE,CAAC,mBAA6B,EAAE,EAAE;QAC7D,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CACzC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CACrD,CAAC;QAEF,uEAAuE;QACvE,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACxD,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,sCAAsC;QACtC,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IACnF,CAAC;CACF,CAAC"}