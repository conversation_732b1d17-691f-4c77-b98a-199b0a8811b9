"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRepository = void 0;
const uuid_1 = require("uuid");
// Mock user data
const users = [
    {
        id: (0, uuid_1.v4)(),
        name: '<PERSON>',
        email: '<EMAIL>',
        partnerId: null, // Will be updated when relationships are created
        createdAt: new Date('2023-01-15')
    },
    {
        id: (0, uuid_1.v4)(),
        name: '<PERSON>',
        email: '<EMAIL>',
        partnerId: null, // Will be updated when relationships are created
        createdAt: new Date('2023-01-20')
    },
    {
        id: (0, uuid_1.v4)(),
        name: '<PERSON>',
        email: '<EMAIL>',
        partnerId: null, // Will be updated when relationships are created
        createdAt: new Date('2023-02-05')
    },
    {
        id: (0, uuid_1.v4)(),
        name: '<PERSON>',
        email: 'jord<PERSON>@example.com',
        partnerId: null, // Will be updated when relationships are created
        createdAt: new Date('2023-02-10')
    }
];
// Mock authentication - in a real app, this would use JWT or similar
const mockTokens = {};
// Simple user repository with CRUD operations
exports.userRepository = {
    // Get all users
    getAll: () => users,
    // Get user by ID
    getById: (id) => users.find(user => user.id === id),
    // Get user by email
    getByEmail: (email) => users.find(user => user.email === email),
    // Create a user - in a real app, this would hash the password
    create: (userData) => {
        const newUser = Object.assign(Object.assign({ id: (0, uuid_1.v4)() }, userData), { createdAt: new Date() });
        users.push(newUser);
        return newUser;
    },
    // Update a user
    update: (id, userData) => {
        const userIndex = users.findIndex(user => user.id === id);
        if (userIndex === -1)
            return null;
        users[userIndex] = Object.assign(Object.assign({}, users[userIndex]), userData);
        return users[userIndex];
    },
    // Delete a user
    delete: (id) => {
        const userIndex = users.findIndex(user => user.id === id);
        if (userIndex === -1)
            return false;
        users.splice(userIndex, 1);
        return true;
    },
    // Link partners
    linkPartners: (userId1, userId2) => {
        const user1Index = users.findIndex(user => user.id === userId1);
        const user2Index = users.findIndex(user => user.id === userId2);
        if (user1Index === -1 || user2Index === -1)
            return false;
        users[user1Index].partnerId = userId2;
        users[user2Index].partnerId = userId1;
        return true;
    },
    // Mock authentication
    authenticate: (email, password) => {
        // In a real app, this would verify the password hash
        const user = users.find(user => user.email === email);
        if (!user)
            return null;
        const token = (0, uuid_1.v4)();
        mockTokens[token] = user.id;
        return { user, token };
    },
    // Validate token
    validateToken: (token) => {
        const userId = mockTokens[token];
        if (!userId)
            return null;
        return users.find(user => user.id === userId);
    }
};
//# sourceMappingURL=users.js.map