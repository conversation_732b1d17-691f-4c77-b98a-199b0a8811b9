"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.relationshipRepository = void 0;
const uuid_1 = require("uuid");
const users_1 = require("./users");
const questions_1 = require("./questions");
// Mock relationships data
const relationships = [];
// Mock daily questions data
const dailyQuestions = [];
// Initialize relationships between users
const initializeRelationships = () => {
    const users = users_1.userRepository.getAll();
    // Create a relationship between the first two users
    if (users.length >= 2) {
        const user1 = users[0];
        const user2 = users[1];
        const relationshipId = (0, uuid_1.v4)();
        const relationship = {
            id: relationshipId,
            userIds: [user1.id, user2.id],
            startDate: new Date('2023-03-01'),
            status: 'active',
            questionHistory: []
        };
        relationships.push(relationship);
        // Link the partners in the user repository
        users_1.userRepository.linkPartners(user1.id, user2.id);
        // Assign a daily question for today
        assignDailyQuestion(relationshipId);
    }
    // Create a relationship between the next two users
    if (users.length >= 4) {
        const user3 = users[2];
        const user4 = users[3];
        const relationshipId = (0, uuid_1.v4)();
        const relationship = {
            id: relationshipId,
            userIds: [user3.id, user4.id],
            startDate: new Date('2023-03-15'),
            status: 'active',
            questionHistory: []
        };
        relationships.push(relationship);
        // Link the partners in the user repository
        users_1.userRepository.linkPartners(user3.id, user4.id);
        // Assign a daily question for today
        assignDailyQuestion(relationshipId);
    }
};
// Assign a daily question to a relationship
const assignDailyQuestion = (relationshipId) => {
    const relationship = exports.relationshipRepository.getById(relationshipId);
    if (!relationship)
        return null;
    // Get all question IDs that have been assigned to this relationship
    const assignedQuestionIds = relationship.questionHistory.map(qh => qh.questionId);
    // Get a random unassigned question
    const question = questions_1.questionRepository.getRandomUnassignedQuestion(assignedQuestionIds);
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    // Create a daily question
    const dailyQuestion = {
        date: dateStr,
        questionId: question.id,
        relationshipId
    };
    // Save the daily question
    dailyQuestions.push(dailyQuestion);
    // Add the question to the relationship's question history
    relationship.questionHistory.push({
        questionId: question.id,
        dateAssigned: today,
        answers: []
    });
    return dailyQuestion;
};
// Simple relationship repository with basic operations
exports.relationshipRepository = {
    // Get all relationships
    getAll: () => relationships,
    // Get relationship by ID
    getById: (id) => relationships.find(r => r.id === id),
    // Get relationship by user ID
    getByUserId: (userId) => relationships.find(r => r.userIds.includes(userId) && r.status === 'active'),
    // Create a relationship
    create: (userIds) => {
        const relationship = {
            id: (0, uuid_1.v4)(),
            userIds,
            startDate: new Date(),
            status: 'active',
            questionHistory: []
        };
        relationships.push(relationship);
        // Link the partners in the user repository
        users_1.userRepository.linkPartners(userIds[0], userIds[1]);
        // Assign a daily question for today
        assignDailyQuestion(relationship.id);
        return relationship;
    },
    // Update a relationship status
    updateStatus: (id, status) => {
        const relationshipIndex = relationships.findIndex(r => r.id === id);
        if (relationshipIndex === -1)
            return null;
        relationships[relationshipIndex].status = status;
        return relationships[relationshipIndex];
    },
    // Add an answer to a relationship's current question
    addAnswer: (relationshipId, userId, content) => {
        const relationship = relationships.find(r => r.id === relationshipId);
        if (!relationship)
            return null;
        // Ensure the user is part of this relationship
        if (!relationship.userIds.includes(userId))
            return null;
        // Get the most recent question history entry
        const currentEntry = relationship.questionHistory[relationship.questionHistory.length - 1];
        if (!currentEntry)
            return null;
        // Check if this user has already answered
        const existingAnswer = currentEntry.answers.find(a => a.userId === userId);
        if (existingAnswer) {
            // Update the existing answer
            existingAnswer.content = content;
            existingAnswer.submittedAt = new Date();
        }
        else {
            // Add a new answer
            currentEntry.answers.push({
                userId,
                content,
                submittedAt: new Date()
            });
        }
        return currentEntry;
    },
    // Get the daily question for a relationship
    getDailyQuestion: (relationshipId) => {
        const today = new Date();
        const dateStr = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
        // Check if there's already a daily question for today
        let dailyQuestion = dailyQuestions.find(dq => dq.relationshipId === relationshipId && dq.date === dateStr);
        // If not, assign a new one
        if (!dailyQuestion) {
            const newDailyQuestion = assignDailyQuestion(relationshipId);
            if (newDailyQuestion) {
                dailyQuestion = newDailyQuestion;
            }
        }
        if (!dailyQuestion)
            return null;
        // Get the full question object
        const question = questions_1.questionRepository.getById(dailyQuestion.questionId);
        return Object.assign(Object.assign({}, dailyQuestion), { question });
    }
};
// Initialize relationships when this module is imported
initializeRelationships();
//# sourceMappingURL=relationships.js.map