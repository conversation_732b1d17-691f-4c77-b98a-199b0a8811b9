{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,iDAA0C;AAC1C,kDAAiD;AAEjD,4DAA4D;AAC5D,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,kBAAkB,CAAC;AAmBzD,MAAM,IAAI,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;;IAC5E,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,KAAK,GAAG,MAAA,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,0CAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,uBAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC;YACH,mBAAmB;YACnB,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAe,CAAC;YAE5D,yBAAyB;YACzB,MAAM,IAAI,GAAG,MAAM,6BAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAEvD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,uBAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YAC5C,CAAC;YAED,0BAA0B;YAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,uBAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;YACtD,CAAC;YAED,sBAAsB;YACtB,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,IAAI,CAAC,GAAG;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC;YAEF,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iCAAiC;YACjC,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,MAAM,IAAI,uBAAQ,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAClD,MAAM,IAAI,uBAAQ,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA/CW,QAAA,IAAI,QA+Cf"}