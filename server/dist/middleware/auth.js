"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const errorHandler_1 = require("./errorHandler");
const repositories_1 = require("../repositories");
// JWT secret key - in production this should be in env vars
const JWT_SECRET = process.env.JWT_SECRET || 'novan-secret-key';
const auth = async (req, res, next) => {
    var _a;
    try {
        // Get token from header
        const token = (_a = req.header('Authorization')) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', '');
        if (!token) {
            throw new errorHandler_1.AppError('Authentication required', 401);
        }
        try {
            // Verify JWT token
            const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
            // Get user from database
            const user = await repositories_1.userRepository.findById(decoded.id);
            if (!user) {
                throw new errorHandler_1.AppError('User not found', 401);
            }
            // Check if user is active
            if (!user.isActive) {
                throw new errorHandler_1.AppError('User account is inactive', 403);
            }
            // Add user to request
            req.user = {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName
            };
            next();
        }
        catch (error) {
            // Handle JWT verification errors
            if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw new errorHandler_1.AppError('Invalid token', 401);
            }
            else if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                throw new errorHandler_1.AppError('Token expired', 401);
            }
            else {
                throw error;
            }
        }
    }
    catch (error) {
        next(error);
    }
};
exports.auth = auth;
//# sourceMappingURL=auth.js.map