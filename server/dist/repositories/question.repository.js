"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.questionRepository = exports.QuestionRepository = void 0;
const base_repository_1 = require("./base.repository");
const Question_1 = __importDefault(require("../models/Question"));
class QuestionRepository extends base_repository_1.BaseRepository {
    constructor() {
        super(Question_1.default);
    }
    async getActive() {
        return this.find({ isActive: true });
    }
    async getByCategory(category) {
        return this.find({ category, isActive: true });
    }
    async deactivate(id) {
        return this.update(id, { isActive: false });
    }
    async getTodayQuestion() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        return this.findOne({
            activeDate: { $gte: today, $lt: tomorrow },
            isActive: true,
        });
    }
    async getRandomUnassignedQuestion(assignedQuestionIds) {
        // Find questions that are active and not in the assignedQuestionIds array
        const availableQuestions = await this.find({
            isActive: true,
            _id: { $nin: assignedQuestionIds }
        });
        if (availableQuestions.length === 0) {
            // If all questions have been assigned, return null
            return null;
        }
        // Return a random unassigned question
        return availableQuestions[Math.floor(Math.random() * availableQuestions.length)];
    }
}
exports.QuestionRepository = QuestionRepository;
// Export a singleton instance
exports.questionRepository = new QuestionRepository();
//# sourceMappingURL=question.repository.js.map