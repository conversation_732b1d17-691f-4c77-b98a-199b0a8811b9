"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRepository = exports.UserRepository = void 0;
const base_repository_1 = require("./base.repository");
const User_1 = __importDefault(require("../models/User"));
class UserRepository extends base_repository_1.BaseRepository {
    constructor() {
        super(User_1.default);
    }
    async findByEmail(email) {
        return this.findOne({ email: email.toLowerCase() });
    }
    async updateLastLogin(id) {
        return this.update(id, { lastLogin: new Date() });
    }
    async setActive(id, isActive) {
        return this.update(id, { isActive });
    }
    async updateProfile(id, profileData) {
        // Filter out sensitive fields that shouldn't be updated through this method
        const { password, email, isActive } = profileData, safeData = __rest(profileData, ["password", "email", "isActive"]);
        return this.update(id, safeData);
    }
    async updatePrivacySettings(id, showAnswers, showProfile) {
        return this.update(id, {
            'preferences.privacySettings.showAnswers': showAnswers,
            'preferences.privacySettings.showProfile': showProfile
        });
    }
}
exports.UserRepository = UserRepository;
// Export a singleton instance
exports.userRepository = new UserRepository();
//# sourceMappingURL=user.repository.js.map