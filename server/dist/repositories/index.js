"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.relationshipRepository = exports.userRepository = exports.questionRepository = void 0;
const question_repository_1 = require("./question.repository");
Object.defineProperty(exports, "questionRepository", { enumerable: true, get: function () { return question_repository_1.questionRepository; } });
const user_repository_1 = require("./user.repository");
Object.defineProperty(exports, "userRepository", { enumerable: true, get: function () { return user_repository_1.userRepository; } });
const relationship_repository_1 = require("./relationship.repository");
Object.defineProperty(exports, "relationshipRepository", { enumerable: true, get: function () { return relationship_repository_1.relationshipRepository; } });
//# sourceMappingURL=index.js.map