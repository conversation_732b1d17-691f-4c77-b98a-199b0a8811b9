"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRepository = void 0;
// Base repository implementation that can be extended by specific repositories
class BaseRepository {
    constructor(model) {
        this.model = model;
    }
    async findAll() {
        return this.model.find();
    }
    async findById(id, populate) {
        if (!populate)
            return this.model.findById(id);
        return this.model.findById(id).populate(populate);
    }
    async findOne(filterQuery) {
        return this.model.findOne(filterQuery);
    }
    async find(filterQuery) {
        return this.model.find(filterQuery);
    }
    async create(item) {
        return this.model.create(item);
    }
    async update(id, item) {
        return this.model.findByIdAndUpdate(id, item, { new: true });
    }
    async delete(id) {
        const result = await this.model.findByIdAndDelete(id);
        return result !== null;
    }
}
exports.BaseRepository = BaseRepository;
//# sourceMappingURL=base.repository.js.map