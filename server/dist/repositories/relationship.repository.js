"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.relationshipRepository = exports.RelationshipRepository = void 0;
const base_repository_1 = require("./base.repository");
const Relationship_1 = __importDefault(require("../models/Relationship"));
class RelationshipRepository extends base_repository_1.BaseRepository {
    constructor() {
        super(Relationship_1.default);
    }
    async getAllAskedQuestions(id) {
        const relationship = await this.findById(id);
        if (!relationship)
            return;
        return relationship.askedQuestions;
    }
    async removeAllAskedQuestions(id) {
        return this.update(id, { alreadyAskedQuestions: [] });
    }
    async findRelationship(userOneId, userTwoId) {
        // Ensure consistent user ID ordering
        const [smallerId, largerId] = userOneId < userTwoId
            ? [userOneId, userTwoId]
            : [userTwoId, userOneId];
        return this.model.findOne({
            userOne: smallerId,
            userTwo: largerId
        }).populate('userOne userTwo');
    }
    async findActiveRelationshipForUser(userId) {
        return this.model.findOne({
            $or: [
                { userOne: userId, status: 'active' },
                { userTwo: userId, status: 'active' }
            ]
        }).populate('userOne userTwo');
    }
    async updateStatus(id, status) {
        const updates = { status };
        // If setting to active, also set the activated date
        if (status === 'active') {
            updates.activatedAt = new Date();
        }
        return this.update(id, updates);
    }
    async incrementInteractions(id) {
        const relationship = await this.findById(id);
        if (!relationship)
            return null;
        relationship.metadata.totalInteractions = (relationship.metadata.totalInteractions || 0) + 1;
        await relationship.save();
        return relationship;
    }
    async updateLastInteraction(id) {
        return this.update(id, { lastInteractionDate: new Date() });
    }
}
exports.RelationshipRepository = RelationshipRepository;
// Export a singleton instance
exports.relationshipRepository = new RelationshipRepository();
//# sourceMappingURL=relationship.repository.js.map