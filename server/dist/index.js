"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const env_1 = require("./config/env");
const errorHandler_1 = require("./middleware/errorHandler");
const connection_1 = __importDefault(require("./database/connection"));
const health_1 = __importDefault(require("./routes/health"));
const user_1 = __importDefault(require("./routes/user"));
const question_1 = __importDefault(require("./routes/question"));
// Initialize express app
const app = (0, express_1.default)();
// Apply middleware
app.use((0, cors_1.default)({
    origin: ['http://localhost:3000', 'http://localhost:3001'], // Allow both common Next.js development ports
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express_1.default.json());
app.use((0, morgan_1.default)('dev'));
// API routes
app.use('/api/health', health_1.default);
app.use('/api/users', user_1.default);
app.use('/api/questions', question_1.default);
// Error handling middleware
app.use(errorHandler_1.errorHandler);
// Connect to MongoDB and start the server
const PORT = env_1.config.port;
const startServer = async () => {
    try {
        // Connect to MongoDB
        await (0, connection_1.default)();
        // Start the server after successful database connection
        app.listen(PORT, () => {
            console.log(`Server running in ${env_1.config.nodeEnv} mode on port ${PORT}`);
        });
    }
    catch (error) {
        console.error(`Failed to start server: ${error.message}`);
        process.exit(1);
    }
};
startServer();
exports.default = app;
//# sourceMappingURL=index.js.map