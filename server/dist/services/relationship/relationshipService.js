"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RelationshipService = void 0;
const repositories_1 = require("../../repositories");
const mongoose_1 = __importDefault(require("mongoose"));
class RelationshipService {
    /**
     * Request a new relationship between users
     */
    static async requestRelationship(initiatorId, recipientId) {
        // Verify both users exist
        const [initiator, recipient] = await Promise.all([
            repositories_1.userRepository.findById(initiatorId),
            repositories_1.userRepository.findById(recipientId)
        ]);
        if (!initiator || !recipient) {
            throw new Error('One or both users not found');
        }
        // Check if users are the same
        if (initiatorId === recipientId) {
            throw new Error('Cannot create relationship with self');
        }
        // Check if either user already has an active relationship
        const [initiatorHasRelation, recipientHasRelation] = await Promise.all([
            repositories_1.relationshipRepository.findActiveRelationshipForUser(initiatorId),
            repositories_1.relationshipRepository.findActiveRelationshipForUser(recipientId)
        ]);
        if (initiatorHasRelation) {
            throw new Error('Initiator already has an active relationship');
        }
        if (recipientHasRelation) {
            throw new Error('Recipient already has an active relationship');
        }
        // Check if a relationship between these users already exists
        const existingRelationship = await repositories_1.relationshipRepository.findRelationship(initiatorId, recipientId);
        if (existingRelationship) {
            // If it's already pending, return it
            if (existingRelationship.status === 'pending') {
                return existingRelationship;
            }
            // If it's blocked or declined, don't allow a new request
            if (existingRelationship.status === 'blocked' || existingRelationship.status === 'declined') {
                throw new Error('Cannot create relationship at this time, User has blocked you.');
            }
            // If it's already active, return it
            if (existingRelationship.status === 'active') {
                return existingRelationship;
            }
        }
        // Create a new pending relationship
        const relationship = await repositories_1.relationshipRepository.create({
            userOne: new mongoose_1.default.Types.ObjectId(initiatorId < recipientId ? initiatorId : recipientId),
            userTwo: new mongoose_1.default.Types.ObjectId(initiatorId < recipientId ? recipientId : initiatorId),
            status: 'pending',
            metadata: {
                initiator: new mongoose_1.default.Types.ObjectId(initiatorId),
            }
        });
        return relationship;
    }
    /**
     * Accept a relationship request
     */
    static async acceptRelationship(userId, relationshipId) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Verify the user is part of this relationship
        console.log('accept relationship', relationship.userOne, relationship.userTwo, userId);
        if (relationship.userOne.toString() !== userId.toString() && relationship.userTwo.toString() !== userId.toString()) {
            throw new Error('User is not part of this relationship');
        }
        // Verify the user is not the initiator (only recipient can accept)
        if (relationship.metadata.initiator.toString() === userId.toString()) {
            throw new Error('Initiator cannot accept their own request');
        }
        // Verify the relationship is pending
        if (relationship.status !== 'pending') {
            throw new Error('Relationship is not in pending status');
        }
        const updatedRelationship = await repositories_1.relationshipRepository.updateStatus(relationshipId, 'active');
        if (!updatedRelationship) {
            throw new Error('Failed to update relationship');
        }
        return updatedRelationship;
    }
    /**
     * Decline a relationship request
     */
    static async declineRelationship(userId, relationshipId) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Verify the user is part of this relationship
        if (relationship.userOne.toString() !== userId.toString() && relationship.userTwo.toString() !== userId.toString()) {
            throw new Error('User is not part of this relationship');
        }
        // Verify the user is not the initiator (only recipient can decline)
        if (relationship.metadata.initiator.toString() === userId.toString()) {
            throw new Error('Initiator cannot decline their own request');
        }
        // Verify the relationship is pending
        if (relationship.status !== 'pending') {
            throw new Error('Relationship is not in pending status');
        }
        // Change status to declined
        const updatedRelationship = await repositories_1.relationshipRepository.updateStatus(relationshipId, 'declined');
        if (!updatedRelationship) {
            throw new Error('Failed to update relationship');
        }
        return updatedRelationship;
    }
    /**
     * Block a relationship
     */
    static async blockRelationship(userId, relationshipId) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Verify the user is part of this relationship
        if (relationship.userOne.toString() !== userId && relationship.userTwo.toString() !== userId) {
            throw new Error('User is not part of this relationship');
        }
        // Change status to blocked
        const updatedRelationship = await repositories_1.relationshipRepository.updateStatus(relationshipId, 'blocked');
        if (!updatedRelationship) {
            throw new Error('Failed to update relationship');
        }
        return updatedRelationship;
    }
    /**
     * Get active relationship for a user
     */
    static async getActiveRelationship(userId) {
        return repositories_1.relationshipRepository.findActiveRelationshipForUser(userId);
    }
    /**
     * Get all relationships for a user (any status)
     */
    static async getAllRelationships(userId) {
        return repositories_1.relationshipRepository.find({
            $or: [
                { userOne: userId },
                { userTwo: userId }
            ]
        });
    }
    /**
     * Update relationship interaction data
     */
    static async recordInteraction(relationshipId) {
        await repositories_1.relationshipRepository.incrementInteractions(relationshipId);
        return repositories_1.relationshipRepository.updateLastInteraction(relationshipId);
    }
    /**
     * Get all asked questions for a relationship
     */
    static async getAllAskedQuestions(relationshipId) {
        return repositories_1.relationshipRepository.getAllAskedQuestions(relationshipId);
    }
    /**
     * Remove all asked questions for a relationship
     */
    static async removeAllAskedQuestions(relationshipId) {
        return repositories_1.relationshipRepository.removeAllAskedQuestions(relationshipId);
    }
    /**
     * Add a question to the askedQuestions array for a relationship
     */
    static async addAskedQuestion(relationshipId, askedQuestion) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Initialize askedQuestions array if it doesn't exist
        if (!relationship.askedQuestions) {
            relationship.askedQuestions = [];
        }
        // Add the new question to the askedQuestions array
        relationship.askedQuestions.push(askedQuestion);
        relationship.alreadyAskedQuestions.push(askedQuestion.questionId);
        // Save the updated relationship
        return repositories_1.relationshipRepository.update(relationshipId, {
            askedQuestions: relationship.askedQuestions,
            alreadyAskedQuestions: relationship.alreadyAskedQuestions
        });
    }
    /**
     * Update answers for a specific asked question in a relationship
     */
    static async updateAskedQuestion(relationshipId, questionId, answers) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Find the index of the asked question
        const askedQuestionIndex = relationship.askedQuestions.findIndex(q => q.questionId.toString() === questionId);
        if (askedQuestionIndex === -1) {
            throw new Error('Asked question not found');
        }
        // Update the answers for the asked question
        relationship.askedQuestions[askedQuestionIndex].answers = answers;
        console.log('relationship.askedQuestions[askedQuestionIndex].answers', relationship.askedQuestions[askedQuestionIndex].answers);
        // Save the updated relationship
        const rel = await repositories_1.relationshipRepository.update(relationshipId, {
            askedQuestions: relationship.askedQuestions
        });
        console.log('rel', rel.askedQuestions[askedQuestionIndex]);
        return rel;
    }
    /**
     * Update most recent asked question with answers in a relationship
     */
    static async updateMostRecentAskedQuestion(relationshipId, answers) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Get the most recent asked question
        const latestAskedQuestion = relationship.askedQuestions[relationship.askedQuestions.length - 1];
        if (!latestAskedQuestion) {
            throw new Error('No asked questions found');
        }
        // Update the answers for the most recent asked question
        latestAskedQuestion.answers = answers;
        // Save the updated relationship
        return repositories_1.relationshipRepository.update(relationshipId, {
            askedQuestions: relationship.askedQuestions
        });
    }
    /**
     * Request a relationship with a user by email
     */
    static async requestRelationshipByEmail(initiatorId, recipientEmail) {
        // Find recipient by email
        const recipient = await repositories_1.userRepository.findOne({ email: recipientEmail.toLowerCase() });
        // If recipient doesn't exist, return a message
        if (!recipient) {
            return { message: 'User not found. Ask your partner to register with this email first.' };
        }
        const recipientId = recipient._id.toString();
        // Cannot create relationship with self
        if (initiatorId === recipientId) {
            return { message: 'You cannot send a relationship request to yourself.' };
        }
        try {
            // Use existing requestRelationship method to create the relationship
            const relationship = await this.requestRelationship(initiatorId, recipientId);
            return { relationship };
        }
        catch (error) {
            return { message: error.message };
        }
    }
    /**
     * Get all pending relationship requests sent by a user
     */
    static async getOutgoingRequests(userId) {
        return repositories_1.relationshipRepository.find({
            $or: [
                { userOne: userId, metadata: { initiator: userId }, status: 'pending' },
                { userTwo: userId, metadata: { initiator: userId }, status: 'pending' }
            ]
        });
    }
    /**
     * Get all pending relationship requests received by a user
     */
    static async getIncomingRequests(userId) {
        return repositories_1.relationshipRepository.find({
            $or: [
                { userOne: userId, metadata: { initiator: { $ne: userId } }, status: 'pending' },
                { userTwo: userId, metadata: { initiator: { $ne: userId } }, status: 'pending' }
            ]
        });
    }
    /**
     * Get pending relationship requests categorized as incoming and outgoing
     */
    static async getAllPendingRequests(userId) {
        // Find all pending relationships
        const allPendingRelationships = await repositories_1.relationshipRepository.find({
            $or: [
                { userOne: userId, status: 'pending' },
                { userTwo: userId, status: 'pending' }
            ]
        });
        // Populate the userOne and userTwo fields
        const populatedRelationships = await Promise.all(allPendingRelationships.map(async (relationship) => {
            return await repositories_1.relationshipRepository.findById(relationship._id.toString(), ['userOne', 'userTwo']);
        }));
        const incoming = populatedRelationships.filter(r => r.metadata.initiator.toString() !== userId.toString());
        const outgoing = populatedRelationships.filter(r => r.metadata.initiator.toString() === userId.toString());
        return { incoming, outgoing };
    }
    /**
     * Get all declined relationship requests
     */
    static async getDeclinedRequests(userId) {
        const relationships = await repositories_1.relationshipRepository.find({
            $or: [
                { userOne: userId, status: 'declined' },
                { userTwo: userId, status: 'declined' }
            ]
        });
        return { declined: relationships };
    }
    /**
     * Cancel a pending relationship request
     */
    static async cancelRequest(userId, relationshipId) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        console.log("user cancel", relationship.userOne, userId);
        // Verify the user is part of this relationship
        if (relationship.userOne.toString() !== userId.toString() && relationship.userTwo.toString() !== userId.toString()) {
            throw new Error('User is not part of this relationship');
        }
        // Verify the user is the initiator (only initiator can cancel)
        if (relationship.metadata.initiator.toString() !== userId.toString()) {
            throw new Error('Only the initiator can cancel a request');
        }
        // Verify the relationship is pending
        if (relationship.status !== 'pending') {
            throw new Error('Only pending requests can be canceled');
        }
        // Delete the relationship
        await repositories_1.relationshipRepository.delete(relationshipId);
        // Return the relationship for reference
        return relationship;
    }
    /**
     * Update timezone preferences for a relationship
     */
    static async updateTimezonePreferences(relationshipId, timezone, preferredQuestionTime) {
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // Update the timezone preferences in metadata
        const updatedMetadata = Object.assign(Object.assign({}, relationship.metadata), { timezone,
            preferredQuestionTime });
        // Save the updated relationship
        return repositories_1.relationshipRepository.update(relationshipId, {
            metadata: updatedMetadata
        });
    }
    /**
     * Check if a new daily question should be assigned based on timezone preferences
     */
    static async shouldAssignNewQuestion(relationshipId) {
        var _a, _b, _c, _d, _e, _f, _g;
        const relationship = await repositories_1.relationshipRepository.findById(relationshipId);
        if (!relationship) {
            throw new Error('Relationship not found');
        }
        // If there are no timezone preferences, use default behavior
        const timezone = ((_a = relationship.metadata) === null || _a === void 0 ? void 0 : _a.timezone) || 'Asia/Kolkata';
        const preferredTime = ((_b = relationship.metadata) === null || _b === void 0 ? void 0 : _b.preferredQuestionTime) || '08:00';
        const latestAskedQuestion = relationship.askedQuestions[relationship.askedQuestions.length - 1];
        // If there are incomplete answers, don't assign a new question yet
        if (latestAskedQuestion.answers.length < 2) {
            return false;
        }
        try {
            // Get the current date and time in UTC
            const currentDateUTC = new Date();
            // Parse preferred time components
            const [hour, minute] = preferredTime.split(':').map(Number);
            // Get the current date in the relationship's timezone
            const dateFormatter = new Intl.DateTimeFormat('en-US', {
                timeZone: timezone,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
            const [{ value: month }, , { value: day }, , { value: year }] = dateFormatter.formatToParts(currentDateUTC);
            // Create today's reset time in the relationship's timezone
            const todayInTimezone = new Date(`${month}/${day}/${year}`);
            todayInTimezone.setHours(hour, minute, 0, 0);
            // Convert the current time to the relationship's timezone
            const currentTimeFormatter = new Intl.DateTimeFormat('en-US', {
                timeZone: timezone,
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
                hour12: false
            });
            const currentTimeParts = currentTimeFormatter.formatToParts(currentDateUTC);
            const currentHour = parseInt(((_c = currentTimeParts.find(part => part.type === 'hour')) === null || _c === void 0 ? void 0 : _c.value) || '0', 10);
            const currentMinute = parseInt(((_d = currentTimeParts.find(part => part.type === 'minute')) === null || _d === void 0 ? void 0 : _d.value) || '0', 10);
            // Get the last question assignment date in the relationship's timezone
            const lastAssignedDate = new Date(latestAskedQuestion.dateAssigned);
            const lastAssignmentDateParts = dateFormatter.formatToParts(lastAssignedDate);
            const lastMonth = (_e = lastAssignmentDateParts.find(part => part.type === 'month')) === null || _e === void 0 ? void 0 : _e.value;
            const lastDay = (_f = lastAssignmentDateParts.find(part => part.type === 'day')) === null || _f === void 0 ? void 0 : _f.value;
            const lastYear = (_g = lastAssignmentDateParts.find(part => part.type === 'year')) === null || _g === void 0 ? void 0 : _g.value;
            if (!lastMonth || !lastDay || !lastYear) {
                return true; // Default to assigning a new question if we can't parse the date
            }
            // Create a date object for comparison
            const lastAssignmentDay = new Date(`${lastMonth}/${lastDay}/${lastYear}`);
            // Check if the current day in the timezone is after the last assigned day
            if (todayInTimezone > lastAssignmentDay) {
                // It's a new day, check if we're past the reset time
                console.log('currentHour', currentHour, 'hour', hour, 'currentMinute', currentMinute, 'minute', minute);
                return currentHour > hour || (currentHour === hour && currentMinute >= minute);
            }
            // It's the same day as the last assignment, don't assign a new question
            return false;
        }
        catch (error) {
            console.error('Error checking if new question should be assigned:', error);
            return true; // On error, default to assigning a new question
        }
    }
}
exports.RelationshipService = RelationshipService;
//# sourceMappingURL=relationshipService.js.map