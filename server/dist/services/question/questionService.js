"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionService = void 0;
const repositories_1 = require("../../repositories");
class QuestionService {
    /**
     * Get all questions
     */
    static async getAllQuestions() {
        return repositories_1.questionRepository.findAll();
    }
    /**
     * Get all active questions
     */
    static async getActiveQuestions() {
        return repositories_1.questionRepository.getActive();
    }
    /**
     * Get question by ID
     */
    static async getQuestionById(id) {
        const question = await repositories_1.questionRepository.findById(id);
        if (!question) {
            throw new Error('Question not found');
        }
        return question;
    }
    /**
     * Get questions by category
     */
    static async getQuestionsByCategory(category) {
        return repositories_1.questionRepository.getByCategory(category);
    }
    /**
     * Create a new question
     */
    static async createQuestion(questionData) {
        const question = await repositories_1.questionRepository.create(Object.assign(Object.assign({}, questionData), { isActive: true }));
        return question;
    }
    /**
     * Update a question
     */
    static async updateQuestion(id, questionData) {
        const question = await repositories_1.questionRepository.update(id, questionData);
        if (!question) {
            throw new Error('Question not found');
        }
        return question;
    }
    /**
     * Deactivate a question (soft delete)
     */
    static async deactivateQuestion(id) {
        const question = await repositories_1.questionRepository.deactivate(id);
        if (!question) {
            throw new Error('Question not found');
        }
        return question;
    }
    /**
     * Get today's active question
     */
    static async getTodayQuestion() {
        const question = await repositories_1.questionRepository.getTodayQuestion();
        if (!question) {
            throw new Error('No active question for today');
        }
        return question;
    }
    /**
     * Get a random unassigned question
     */
    static async getRandomUnassignedQuestion(assignedQuestionIds) {
        return repositories_1.questionRepository.getRandomUnassignedQuestion(assignedQuestionIds);
    }
}
exports.QuestionService = QuestionService;
//# sourceMappingURL=questionService.js.map