"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const repositories_1 = require("../../repositories");
const crypto_1 = __importDefault(require("crypto"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// Secret key for JWT token generation - in production this should be in env vars
const JWT_SECRET = process.env.JWT_SECRET || 'novan-secret-key';
class UserService {
    /**
     * Register a new user
     */
    static async register(userData) {
        // Check if user already exists
        const existingUser = await repositories_1.userRepository.findByEmail(userData.email);
        if (existingUser) {
            throw new Error('User with this email already exists');
        }
        // Hash the password
        const hashedPassword = await this.hashPassword(userData.password);
        // Create the new user
        const user = await repositories_1.userRepository.create(Object.assign(Object.assign({}, userData), { password: hashedPassword, isActive: true, preferences: {
                emailNotifications: true,
                privacySettings: {
                    showAnswers: true,
                    showProfile: true,
                },
            }, lastLogin: new Date() }));
        // Generate JWT token
        const token = this.generateToken(user);
        // Return user (without password) and token
        const _a = user.toObject(), { password } = _a, userWithoutPassword = __rest(_a, ["password"]);
        return { user: userWithoutPassword, token };
    }
    /**
     * Login a user
     */
    static async login(email, password) {
        // Find user by email
        const user = await repositories_1.userRepository.findByEmail(email);
        if (!user) {
            throw new Error('Invalid credentials');
        }
        // Check if password matches
        const isPasswordValid = await this.verifyPassword(password, user.password);
        if (!isPasswordValid) {
            throw new Error('Invalid credentials');
        }
        // Update last login timestamp
        await repositories_1.userRepository.updateLastLogin(user._id.toString());
        // Generate JWT token
        const token = this.generateToken(user);
        // Return user (without password) and token
        const _a = user.toObject(), { password: _ } = _a, userWithoutPassword = __rest(_a, ["password"]);
        return { user: userWithoutPassword, token };
    }
    /**
     * Get user profile by ID
     */
    static async getProfile(userId) {
        const user = await repositories_1.userRepository.findById(userId);
        if (!user) {
            throw new Error('User not found');
        }
        // Return user without sensitive information
        const _a = user.toObject(), { password } = _a, userWithoutPassword = __rest(_a, ["password"]);
        return userWithoutPassword;
    }
    /**
     * Update user profile
     */
    static async updateProfile(userId, profileData) {
        const updatedUser = await repositories_1.userRepository.updateProfile(userId, profileData);
        if (!updatedUser) {
            throw new Error('Failed to update profile');
        }
        // Return user without sensitive information
        const _a = updatedUser.toObject(), { password } = _a, userWithoutPassword = __rest(_a, ["password"]);
        return userWithoutPassword;
    }
    /**
     * Update user privacy settings
     */
    static async updatePrivacySettings(userId, settings) {
        const updatedUser = await repositories_1.userRepository.updatePrivacySettings(userId, settings.showAnswers, settings.showProfile);
        if (!updatedUser) {
            throw new Error('Failed to update privacy settings');
        }
        // Return user without sensitive information
        const _a = updatedUser.toObject(), { password } = _a, userWithoutPassword = __rest(_a, ["password"]);
        return userWithoutPassword;
    }
    /**
     * Hash a password
     */
    static async hashPassword(password) {
        return new Promise((resolve, reject) => {
            const salt = crypto_1.default.randomBytes(16).toString('hex');
            crypto_1.default.scrypt(password, salt, 64, (err, derivedKey) => {
                if (err)
                    reject(err);
                resolve(salt + ':' + derivedKey.toString('hex'));
            });
        });
    }
    /**
     * Verify a password against a hash
     */
    static async verifyPassword(password, hash) {
        return new Promise((resolve, reject) => {
            const [salt, key] = hash.split(':');
            crypto_1.default.scrypt(password, salt, 64, (err, derivedKey) => {
                if (err)
                    reject(err);
                resolve(key === derivedKey.toString('hex'));
            });
        });
    }
    /**
     * Generate a JWT token for a user
     */
    static generateToken(user) {
        return jsonwebtoken_1.default.sign({
            id: user._id,
            email: user.email,
        }, JWT_SECRET, { expiresIn: '7d' });
    }
}
exports.UserService = UserService;
//# sourceMappingURL=userService.js.map