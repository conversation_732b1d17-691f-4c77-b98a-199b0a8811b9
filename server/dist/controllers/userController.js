"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userController = void 0;
const services_1 = require("../services");
const errorHandler_1 = require("../middleware/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
exports.userController = {
    // Register a new user
    register: async (req, res, next) => {
        try {
            const { firstName, lastName, email, password } = req.body;
            if (!firstName || !lastName || !email || !password) {
                throw new errorHandler_1.AppError('First name, last name, email, and password are required', 400);
            }
            // Register user using the user service
            const { user, token } = await services_1.UserService.register({
                firstName,
                lastName,
                email,
                password
            });
            res.status(201).json({
                status: 'success',
                data: {
                    user,
                    token
                }
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Login user
    login: async (req, res, next) => {
        try {
            const { email, password } = req.body;
            if (!email || !password) {
                throw new errorHandler_1.AppError('Email and password are required', 400);
            }
            // Login using the user service
            const { user, token } = await services_1.UserService.login(email, password);
            // Get active relationship if exists
            const relationship = await services_1.RelationshipService.getActiveRelationship(user._id.toString());
            // Include partner info in response
            const responseUser = Object.assign(Object.assign({}, user), { partner: relationship ? {
                    id: relationship.userOne.toString() === user._id.toString()
                        ? relationship.userTwo._id
                        : relationship.userOne._id,
                    status: relationship.status
                } : null });
            res.status(200).json({
                status: 'success',
                data: {
                    user: responseUser,
                    token
                }
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Get current user profile
    getProfile: async (req, res, next) => {
        try {
            const userId = req.user.id;
            // Get user profile
            const user = await services_1.UserService.getProfile(userId);
            // Get active relationship if exists
            const relationship = await services_1.RelationshipService.getActiveRelationship(userId);
            // Include partner info in response
            const responseUser = Object.assign(Object.assign({}, user), { partner: relationship ? {
                    id: relationship.userOne._id.toString() === userId.toString()
                        ? relationship.userTwo._id
                        : relationship.userOne._id,
                    status: relationship.status
                } : null });
            res.status(200).json({
                status: 'success',
                data: {
                    user: responseUser,
                    relationship
                }
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Update user profile
    updateProfile: async (req, res, next) => {
        try {
            const { firstName, lastName, bio } = req.body;
            const userId = req.user.id;
            const updatedProfile = Object.assign(Object.assign(Object.assign({}, (firstName && { firstName })), (lastName && { lastName })), (bio && { bio }));
            // Update user profile
            const user = await services_1.UserService.updateProfile(userId, updatedProfile);
            // Get active relationship if exists
            const relationship = await services_1.RelationshipService.getActiveRelationship(userId);
            // Include partner info in response
            const responseUser = Object.assign(Object.assign({}, user), { partner: relationship ? {
                    id: relationship.userOne._id.toString() === userId.toString()
                        ? relationship.userTwo._id
                        : relationship.userOne._id,
                    status: relationship.status
                } : null });
            res.status(200).json({
                status: 'success',
                data: {
                    user: responseUser
                }
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Update user privacy settings
    updatePrivacySettings: async (req, res, next) => {
        try {
            const { showAnswers, showProfile } = req.body;
            const userId = req.user.id;
            if (typeof showAnswers !== 'boolean' || typeof showProfile !== 'boolean') {
                throw new errorHandler_1.AppError('Invalid privacy settings', 400);
            }
            // Update privacy settings
            const user = await services_1.UserService.updatePrivacySettings(userId, {
                showAnswers,
                showProfile
            });
            res.status(200).json({
                status: 'success',
                data: {
                    user
                }
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Send a relationship request to another user by email
    sendRelationshipRequest: async (req, res) => {
        try {
            const { email } = req.body;
            const userId = req.user.id;
            if (!email) {
                res.status(400).json({ message: 'Email is required' });
                return;
            }
            const result = await services_1.RelationshipService.requestRelationshipByEmail(userId, email);
            if (result.message) {
                res.status(400).json({ message: result.message });
                return;
            }
            res.status(201).json({
                data: {
                    message: 'Relationship request sent successfully',
                    relationship: result.relationship
                }
            });
        }
        catch (error) {
            res.status(500).json({ message: error.message });
        }
    },
    // Get pending relationships (both incoming and outgoing)
    getPendingRelationships: async (req, res) => {
        try {
            const userId = req.user.id;
            const hasActiveRelationship = await services_1.RelationshipService.getActiveRelationship(userId);
            if (hasActiveRelationship) {
                res.status(203).json({ data: hasActiveRelationship });
                return;
            }
            const pendingRelationships = await services_1.RelationshipService.getAllPendingRequests(userId);
            const declinedRelationships = await services_1.RelationshipService.getDeclinedRequests(userId);
            res.status(200).json({ data: Object.assign(Object.assign({}, pendingRelationships), declinedRelationships) });
        }
        catch (error) {
            res.status(500).json({ message: error.message });
        }
    },
    // Accept a relationship request
    acceptRelationshipRequest: async (req, res) => {
        try {
            const { relationshipId } = req.body;
            const userId = req.user.id;
            if (!relationshipId || !mongoose_1.default.Types.ObjectId.isValid(relationshipId)) {
                res.status(400).json({ message: 'Valid relationship ID is required' });
                return;
            }
            const relationship = await services_1.RelationshipService.acceptRelationship(userId, relationshipId);
            res.status(200).json({
                data: {
                    message: 'Relationship request accepted',
                    relationship
                }
            });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    },
    // Decline a relationship request
    declineRelationshipRequest: async (req, res) => {
        try {
            const { relationshipId } = req.params;
            const userId = req.user.id;
            if (!relationshipId || !mongoose_1.default.Types.ObjectId.isValid(relationshipId)) {
                res.status(400).json({ message: 'Valid relationship ID is required' });
                return;
            }
            const relationship = await services_1.RelationshipService.declineRelationship(userId, relationshipId);
            res.status(200).json({
                data: {
                    message: 'Relationship request declined',
                    relationship
                }
            });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    },
    // Cancel an outgoing relationship request
    cancelRelationshipRequest: async (req, res) => {
        try {
            const { relationshipId } = req.params;
            const userId = req.user.id;
            if (!relationshipId || !mongoose_1.default.Types.ObjectId.isValid(relationshipId)) {
                res.status(400).json({ message: 'Valid relationship ID is required' });
                return;
            }
            await services_1.RelationshipService.cancelRequest(userId, relationshipId);
            res.status(200).json({
                data: {
                    message: 'Relationship request canceled'
                }
            });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    },
    // Update timezone preferences for an active relationship
    updateTimezonePreferences: async (req, res) => {
        try {
            const userId = req.user.id;
            const { timezone, preferredQuestionTime } = req.body;
            if (!timezone || !preferredQuestionTime) {
                res.status(400).json({ message: 'Timezone and preferred question time are required' });
                return;
            }
            // Get the user's active relationship
            const relationship = await services_1.RelationshipService.getActiveRelationship(userId);
            if (!relationship) {
                res.status(404).json({ message: 'No active relationship found' });
                return;
            }
            // Update the timezone preferences
            const updatedRelationship = await services_1.RelationshipService.updateTimezonePreferences(relationship._id.toString(), timezone, preferredQuestionTime);
            res.status(200).json({
                data: {
                    message: 'Timezone preferences updated successfully',
                    relationship: updatedRelationship
                }
            });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    }
};
//# sourceMappingURL=userController.js.map