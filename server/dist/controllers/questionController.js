"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.questionController = void 0;
const errorHandler_1 = require("../middleware/errorHandler");
const services_1 = require("../services");
exports.questionController = {
    // Get daily question for the current user
    getDailyQuestion: async (req, res, next) => {
        var _a, _b;
        try {
            const userIdObj = req.user.id;
            // Get the user's relationship from the database
            const relationship = await services_1.RelationshipService.getActiveRelationship(userIdObj);
            if (!relationship) {
                throw new errorHandler_1.AppError('No active relationship found', 404);
            }
            let todayQuestion = null;
            // Check if we should assign a new question based on timezone preferences
            const shouldAssignNew = await services_1.RelationshipService.shouldAssignNewQuestion(relationship._id.toString());
            if (!shouldAssignNew) {
                const latestAskedQuestion = relationship.askedQuestions[relationship.askedQuestions.length - 1];
                // Use the existing question if we shouldn't assign a new one
                todayQuestion = await services_1.QuestionService.getQuestionById(latestAskedQuestion.questionId.toString());
            }
            // If no question for today, get a random unassigned question and add it to askedQuestions
            if (shouldAssignNew) {
                const askedQuestionIds = relationship.alreadyAskedQuestions.map(q => q.toString());
                // Get a random unassigned question
                todayQuestion = await services_1.QuestionService.getRandomUnassignedQuestion(askedQuestionIds);
                // If all questions have been assigned, get a new one
                if (!todayQuestion) {
                    await services_1.RelationshipService.removeAllAskedQuestions(relationship._id.toString());
                    todayQuestion = await services_1.QuestionService.getRandomUnassignedQuestion([]);
                }
                // Get today's date in the relationship's timezone
                const timezone = ((_a = relationship.metadata) === null || _a === void 0 ? void 0 : _a.timezone) || 'Asia/Kolkata';
                const today = new Date();
                const todayInTimezone = new Date(today.toLocaleString('en-US', { timeZone: timezone }));
                const prefferedTime = ((_b = relationship.metadata) === null || _b === void 0 ? void 0 : _b.preferredQuestionTime) || '08:00';
                const [hour, minute] = prefferedTime.split(':').map(Number);
                todayInTimezone.setHours(hour, minute, 0, 0);
                // Add the new question to askedQuestions
                const newAskedQuestion = {
                    questionId: todayQuestion._id,
                    dateAssigned: todayInTimezone,
                    answers: []
                };
                // Update the relationship with the new asked question
                await services_1.RelationshipService.addAskedQuestion(relationship._id.toString(), newAskedQuestion);
            }
            // Get partner's ID
            const partnerId = relationship.userOne._id.toString() === userIdObj.toString()
                ? relationship.userTwo._id.toString()
                : relationship.userOne._id.toString();
            // instead of using AnswerService use askedQuestions array from the relationship
            const userAnswer = relationship.askedQuestions.length > 0 ? relationship.askedQuestions[relationship.askedQuestions.length - 1].answers.find(a => a.userId.toString() === userIdObj.toString()) : null;
            const partnerAnswer = relationship.askedQuestions.length > 0 ? relationship.askedQuestions[relationship.askedQuestions.length - 1].answers.find(a => a.userId.toString() === partnerId) : null;
            const bothAnswered = userAnswer && partnerAnswer;
            // Format the response
            const response = {
                id: todayQuestion._id,
                text: todayQuestion.text,
                category: todayQuestion.category,
                inputType: todayQuestion.description ? 'text' : 'text', // Default to text if no specific type
                options: todayQuestion.tags || [], // Using tags as options if needed
                maxLength: 1000, // Default value
                date: todayQuestion.activeDate,
                userAnswer: userAnswer ? userAnswer.content : null,
                partnerAnswer: partnerAnswer ? bothAnswered ? partnerAnswer.content : 'Not visible yet' : null,
                bothAnswered: bothAnswered ? true : false
            };
            res.status(200).json({
                status: 'success',
                data: { question: response }
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Submit an answer to the daily question
    submitAnswer: async (req, res, next) => {
        try {
            const userIdObj = req.user.id;
            const { answer } = req.body;
            if (!answer) {
                throw new errorHandler_1.AppError('Answer is required', 400);
            }
            // Get the user's relationship
            const relationship = await services_1.RelationshipService.getActiveRelationship(userIdObj);
            if (!relationship) {
                throw new errorHandler_1.AppError('No active relationship found', 404);
            }
            // Check if there's a question assigned for today
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            // Ensure askedQuestions array exists
            if (!relationship.askedQuestions || relationship.askedQuestions.length === 0) {
                throw new errorHandler_1.AppError('No question available for today', 404);
            }
            // Get the latest asked question
            const latestAskedQuestion = relationship.askedQuestions.pop();
            // const assignedDate = new Date(latestAskedQuestion.dateAssigned);
            // assignedDate.setHours(0, 0, 0, 0);
            // Check if the latest question was assigned today
            // if (assignedDate.getTime() !== today.getTime()) {
            //   throw new AppError('No question available for today', 404);
            // }
            // Get the question details
            const todayQuestion = await services_1.QuestionService.getQuestionById(latestAskedQuestion.questionId.toString());
            if (!todayQuestion) {
                throw new errorHandler_1.AppError('Question not found', 404);
            }
            // Get partner's ID
            const partnerId = relationship.userOne._id.toString() === userIdObj.toString()
                ? relationship.userTwo._id.toString()
                : relationship.userOne._id.toString();
            // Check if user has already answered
            const existingUserAnswerIndex = latestAskedQuestion.answers.findIndex(a => a.userId.toString() === userIdObj.toString());
            // Create new answer object
            const newAnswer = {
                userId: userIdObj,
                content: answer,
                submittedAt: new Date()
            };
            // Update or add the user's answer
            if (existingUserAnswerIndex !== -1) {
                // Update existing answer
                latestAskedQuestion.answers[existingUserAnswerIndex] = newAnswer;
            }
            else {
                // Add new answer
                latestAskedQuestion.answers.push(newAnswer);
            }
            // Update the relationship with the new answer
            const rel = await services_1.RelationshipService.updateMostRecentAskedQuestion(relationship._id.toString(), latestAskedQuestion.answers);
            // Record the interaction in the relationship
            await services_1.RelationshipService.recordInteraction(relationship._id.toString());
            // Check if partner has answered
            const partnerAnswer = latestAskedQuestion.answers.find(a => a.userId.toString() === partnerId);
            const bothAnswered = partnerAnswer !== undefined;
            // Format the response
            const response = {
                submitted: true,
                bothAnswered,
                partnerAnswer: partnerAnswer ? partnerAnswer.content : null
            };
            res.status(200).json({
                status: 'success',
                data: response
            });
        }
        catch (error) {
            next(error);
        }
    },
    // Get answer history for a relationship, this will also accept an optional month and year query parameters
    getAnswerHistory: async (req, res, next) => {
        var _a;
        try {
            const userIdObj = req.user.id;
            const month = req.query.month ? parseInt(req.query.month) : null;
            const year = req.query.year ? parseInt(req.query.year) : null;
            // Get the user's relationship
            const relationship = await services_1.RelationshipService.getActiveRelationship(userIdObj);
            if (!relationship) {
                throw new errorHandler_1.AppError('No active relationship found', 404);
            }
            // Get partner's ID
            const partnerId = relationship.userOne._id.toString() === userIdObj.toString()
                ? relationship.userTwo._id.toString()
                : relationship.userOne._id.toString();
            // Get the user's timezone preference
            const timezone = ((_a = relationship.metadata) === null || _a === void 0 ? void 0 : _a.timezone) || 'Asia/Kolkata';
            // Use the askedQuestions array from the relationship
            if (!relationship.askedQuestions || relationship.askedQuestions.length === 0) {
                return res.status(200).json({
                    status: 'success',
                    data: { history: [], totalCount: 0 }
                });
            }
            // Sort askedQuestions by dateAssigned (newest first)
            const askedQuestions = relationship.askedQuestions;
            // Format the history response
            const history = await Promise.all(askedQuestions.map(async (askedQuestion) => {
                // Get the question details
                const question = await services_1.QuestionService.getQuestionById(askedQuestion.questionId.toString());
                if (!question) {
                    return null; // Skip if question not found
                }
                const questionDate = askedQuestion.dateAssigned;
                // Create a date formatted in the user's timezone
                const dateFormatter = new Intl.DateTimeFormat('en-US', {
                    timeZone: timezone,
                    year: 'numeric',
                    month: 'numeric',
                    day: 'numeric'
                });
                // Format the date in user's timezone
                const formattedDate = dateFormatter.format(new Date(questionDate));
                const [formattedMonth, formattedDay, formattedYear] = formattedDate.split('/');
                // Parse the month and year for filtering
                const localMonth = parseInt(formattedMonth);
                const localYear = parseInt(formattedYear);
                // Apply month/year filter if provided
                if (month && year) {
                    if (localMonth !== month || localYear !== year) {
                        return null; // Skip if not in the specified month and year
                    }
                }
                // Find user and partner answers from the askedQuestions
                const userAnswer = askedQuestion.answers.find(a => a.userId.toString() === userIdObj.toString());
                const partnerAnswer = askedQuestion.answers.find(a => a.userId.toString() === partnerId);
                return {
                    date: `${localYear}-${localMonth}-${parseInt(formattedDay)}`,
                    question: {
                        id: question._id,
                        text: question.text,
                        category: question.category,
                        inputType: question.description ? 'text' : 'text' // Default to text
                    },
                    userAnswer: userAnswer ? userAnswer.content : null,
                    partnerAnswer: partnerAnswer ? partnerAnswer.content : null,
                    bothAnswered: userAnswer && partnerAnswer ? true : false
                };
            }));
            // Filter out null entries (questions that weren't found or filtered out)
            const validHistory = history.filter(entry => entry !== null);
            // Sort history by date, most recent first
            validHistory.sort((a, b) => {
                return new Date(a.date).getTime() - new Date(b.date).getTime();
            });
            // total count
            const totalCount = validHistory.length;
            // Get available years and months starting from relationship's activatedAt date
            const availableYears = [];
            const availableMonths = {};
            // Get activation date and current date
            const activationDate = new Date(relationship.activatedAt);
            const activationYear = activationDate.getFullYear();
            const activationMonth = activationDate.getMonth() + 1; // 1-12
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1; // 1-12
            // Generate available years
            for (let year = activationYear; year <= currentYear; year++) {
                availableYears.push(year);
                // Generate available months for each year
                availableMonths[year] = [];
                const startMonth = year === activationYear ? activationMonth : 1;
                const endMonth = year === currentYear ? currentMonth : 12;
                for (let month = startMonth; month <= endMonth; month++) {
                    availableMonths[year].push(month);
                }
            }
            res.status(200).json({
                status: 'success',
                data: {
                    history: validHistory,
                    totalCount,
                    availableYears,
                    availableMonths
                }
            });
        }
        catch (error) {
            next(error);
        }
    }
};
//# sourceMappingURL=questionController.js.map