import { Router } from 'express';
import { userController } from '../controllers/userController';
import { auth } from '../middleware/auth';

const router = Router();

// Public routes
router.post('/register', userController.register);
router.post('/login', userController.login);

// Protected routes (require authentication)
router.get('/profile', auth, userController.getProfile);
router.put('/profile', auth, userController.updateProfile);
router.put('/privacy-settings', auth, userController.updatePrivacySettings);

// Relationship routes
router.post('/relationship/request', auth, userController.sendRelationshipRequest);
router.get('/relationship/pending', auth, userController.getPendingRelationships);
router.post('/relationship/:relationshipId/accept', auth, userController.acceptRelationshipRequest);
router.post('/relationship/:relationshipId/decline', auth, userController.declineRelationshipRequest);
router.delete('/relationship/:relationshipId/cancel', auth, userController.cancelRelationshipRequest);
router.put('/relationship/timezone', auth, userController.updateTimezonePreferences);

export default router;