import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import { config } from './config/env';
import { errorHandler } from './middleware/errorHandler';
import connectDB from './database/connection';
import healthRoutes from './routes/health';
import userRoutes from './routes/user';
import questionRoutes from './routes/question';

// Initialize express app
const app = express();

// Apply middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'], // Allow both common Next.js development ports
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(morgan('dev'));

// API routes
app.use('/api/health', healthRoutes);
app.use('/api/users', userRoutes);
app.use('/api/questions', questionRoutes);

// Error handling middleware
app.use(errorHandler);

// Connect to MongoDB and start the server
const PORT = config.port;

const startServer = async () => {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Start the server after successful database connection
    app.listen(PORT, () => {
      console.log(`Server running in ${config.nodeEnv} mode on port ${PORT}`);
    });
  } catch (error: any) {
    console.error(`Failed to start server: ${error.message}`);
    process.exit(1);
  }
};

startServer();

export default app;