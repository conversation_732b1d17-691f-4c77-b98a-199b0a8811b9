import mongoose, { Document, Model, Schema } from 'mongoose';
import { IUser } from './User';
import { AskedQuestion } from '../types';

export interface IRelationship extends Document {
  userOne: mongoose.Types.ObjectId | IUser;
  userTwo: mongoose.Types.ObjectId | IUser;
  status: 'pending' | 'active' | 'blocked' | 'declined';
  createdAt: Date;
  updatedAt: Date;
  activatedAt?: Date;
  lastInteractionDate?: Date;
  askedQuestions: AskedQuestion[]; // askedQuestions contains the history of questions asked in this relationship
  alreadyAskedQuestions: mongoose.Types.ObjectId[]; // and this will be used to track which questions are asked so that we don't ask the same question twice and if once all questions are asked, we can start over
  metadata: {
    initiator: mongoose.Types.ObjectId | IUser;
    questionStreak?: number;
    totalInteractions?: number;
    compatibilityScore?: number;
    notes?: string;
    timezone?: string; // Standard timezone name (e.g., "America/New_York")
    preferredQuestionTime?: string; // Time in 24h format (e.g., "08:30")
  };
}

const RelationshipSchema: Schema = new Schema(
  {
    userOne: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    userTwo: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'active', 'blocked', 'declined'],
      default: 'pending',
    },
    activatedAt: {
      type: Date,
    },
    lastInteractionDate: {
      type: Date,
    },
    askedQuestions: [{
      questionId: {
        type: Schema.Types.ObjectId,
        ref: 'Question',
        required: true,
      },
      dateAssigned: {
        type: Date,
        default: new Date().toISOString().split('T')[0],
      },
      answers: [{
        userId: {
          type: Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        content: {
          type: String,
        },
        submittedAt: {
          type: Date,
          default: Date.now,
        },
      }],
    }],
    alreadyAskedQuestions: [{
      type: Schema.Types.ObjectId,
      ref: 'Question',
    }],
    metadata: {
      initiator: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      questionStreak: {
        type: Number,
        default: 0,
      },
      totalInteractions: {
        type: Number,
        default: 0,
      },
      compatibilityScore: {
        type: Number,
        min: 0,
        max: 100,
      },
      notes: {
        type: String,
      },
      timezone: {
        type: String,
        default: "Asia/Kolkata" // default timezone
      },
      preferredQuestionTime: {
        type: String,
        default: "08:00" // default to 8:00 AM
      }
    },
  },
  {
    timestamps: true,
  }
);

// Ensure each relationship between two users is unique
RelationshipSchema.index(
  { 
    userOne: 1, 
    userTwo: 1 
  }, 
  { 
    unique: true 
  }
);

// For finding relationships by user
RelationshipSchema.index({ userOne: 1, status: 1 });
RelationshipSchema.index({ userTwo: 1, status: 1 });

// Static method to find the active relationship for a user
RelationshipSchema.statics.findActiveRelationshipForUser = async function(userId) {
  return this.findOne({
    $or: [
      { userOne: userId, status: 'active' },
      { userTwo: userId, status: 'active' }
    ]
  }).populate('userOne userTwo');
};

// Check if a user has any active relationship
RelationshipSchema.statics.hasActiveRelationship = async function(userId) {
  const count = await this.countDocuments({
    $or: [
      { userOne: userId, status: 'active' },
      { userTwo: userId, status: 'active' }
    ]
  });
  return count > 0;
};

// Pre-save middleware to ensure userOne is always lexicographically smaller than userTwo
// This ensures consistent relationship lookup and prevents duplicate relationships
RelationshipSchema.pre('save', async function(this: Document & IRelationship, next) {
  const userOneOriginal = this.userOne;
  const userTwoOriginal = this.userTwo;

  const userOne = userOneOriginal.toString();
  const userTwo = userTwoOriginal.toString();
  console.log('userOne', userOne, 'userTwo', userTwo);
  if (userOne === userTwo) {
    const err = new Error('Wait! Cannot create relationship with self');
    return next(err);
  }
  
  // Always store the smaller ID in userOne for consistent querying
  if (userOne > userTwo) {
    this.userOne = userTwoOriginal;
    this.userTwo = userOneOriginal;
  }
  
  // If status is being changed to active, ensure neither user is already in an active relationship
  if (this.isModified('status') && this.status === 'active') {
    this.activatedAt = new Date();
    
    // Check if either user already has an active relationship
    const RelationshipModel = mongoose.model('Relationship');
    
    const [userOneHasActiveRelation, userTwoHasActiveRelation] = await Promise.all([
      RelationshipModel.countDocuments({
        $or: [
          { userOne: this.userOne, status: 'active', _id: { $ne: this._id } },
          { userTwo: this.userOne, status: 'active', _id: { $ne: this._id } }
        ]
      }),
      RelationshipModel.countDocuments({
        $or: [
          { userOne: this.userTwo, status: 'active', _id: { $ne: this._id } },
          { userTwo: this.userTwo, status: 'active', _id: { $ne: this._id } }
        ]
      })
    ]);
    
    if (userOneHasActiveRelation) {
      const err = new Error('User One already has an active relationship');
      return next(err);
    }
    
    if (userTwoHasActiveRelation) {
      const err = new Error('User Two already has an active relationship');
      return next(err);
    }
  }
  
  next();
});

interface IRelationshipModel extends Model<IRelationship> {
  findActiveRelationshipForUser(userId: string): Promise<IRelationship | null>;
  hasActiveRelationship(userId: string): Promise<number>;
}

export default mongoose.model<IRelationship, IRelationshipModel>('Relationship', RelationshipSchema);

