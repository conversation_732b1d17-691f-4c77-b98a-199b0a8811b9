import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
  isActive: boolean;
  relationshipId: mongoose.Types.ObjectId;
  preferences: {
    emailNotifications: boolean;
    privacySettings: {
      showAnswers: boolean;
      showProfile: boolean;
    };
  };
}

const UserSchema: Schema = new Schema(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    profilePicture: {
      type: String,
    },
    bio: {
      type: String,
      maxlength: 500,
    },
    lastLogin: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    relationshipId: {
      type: Schema.Types.ObjectId,
      ref: 'Relationship',
    },
    preferences: {
      emailNotifications: {
        type: Boolean,
        default: true,
      },
      privacySettings: {
        showAnswers: {
          type: Boolean,
          default: true,
        },
        showProfile: {
          type: Boolean,
          default: true,
        },
      },
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Create indexes for faster queries
UserSchema.index({ createdAt: -1 });

export default mongoose.model<IUser>('User', UserSchema);