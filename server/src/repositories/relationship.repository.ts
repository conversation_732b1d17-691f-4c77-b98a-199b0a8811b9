import { BaseRepository } from './base.repository';
import Relationship, { IRelationship } from '../models/Relationship';
import Question, { IQuestion } from '../models/Question';
import { AskedQuestion } from '../types';
import mongoose from 'mongoose';

export interface IRelationshipRepository extends BaseRepository<IRelationship> {
  findRelationship(userOneId: string, userTwoId: string): Promise<IRelationship | null>;
  findActiveRelationshipForUser(userId: string): Promise<IRelationship | null>;
  updateStatus(id: string, status: 'pending' | 'active' | 'blocked' | 'declined'): Promise<IRelationship | null>;
  incrementInteractions(id: string): Promise<IRelationship | null>;
  updateLastInteraction(id: string): Promise<IRelationship | null>;
  getAllAskedQuestions(id: string): Promise<AskedQuestion[]>;
}

export class RelationshipRepository extends BaseRepository<IRelationship> implements IRelationshipRepository {
  constructor() {
    super(Relationship);
  }

  async getAllAskedQuestions(id: string): Promise<AskedQuestion[]> {
    const relationship = await this.findById(id);
    if (!relationship) return;
    return relationship.askedQuestions;
  }

  async removeAllAskedQuestions(id: string): Promise<IRelationship | null> {
    return this.update(id, { alreadyAskedQuestions: [] });
  }

  async findRelationship(userOneId: string, userTwoId: string): Promise<IRelationship | null> {
    // Ensure consistent user ID ordering
    const [smallerId, largerId] = userOneId < userTwoId 
      ? [userOneId, userTwoId] 
      : [userTwoId, userOneId];
    
    return this.model.findOne({ 
      userOne: smallerId,
      userTwo: largerId
    }).populate('userOne userTwo');
  }

  async findActiveRelationshipForUser(userId: string): Promise<IRelationship | null> {
    return this.model.findOne({
      $or: [
        { userOne: userId, status: 'active' },
        { userTwo: userId, status: 'active' }
      ]
    }).populate('userOne userTwo');
  }

  async updateStatus(id: string, status: 'pending' | 'active' | 'blocked' | 'declined'): Promise<IRelationship | null> {
    const updates: any = { status };
    
    // If setting to active, also set the activated date
    if (status === 'active') {
      updates.activatedAt = new Date();
    }
    
    return this.update(id, updates);
  }

  async incrementInteractions(id: string): Promise<IRelationship | null> {
    const relationship = await this.findById(id);
    if (!relationship) return null;
    
    relationship.metadata.totalInteractions = (relationship.metadata.totalInteractions || 0) + 1;
    await relationship.save();
    
    return relationship;
  }

  async updateLastInteraction(id: string): Promise<IRelationship | null> {
    return this.update(id, { lastInteractionDate: new Date() });
  }

}

// Export a singleton instance
export const relationshipRepository = new RelationshipRepository();