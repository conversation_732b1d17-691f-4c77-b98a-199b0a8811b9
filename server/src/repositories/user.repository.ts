import { BaseRepository } from './base.repository';
import User, { IUser } from '../models/User';

export interface IUserRepository extends BaseRepository<IUser> {
  findByEmail(email: string): Promise<IUser | null>;
  updateLastLogin(id: string): Promise<IUser | null>;
  setActive(id: string, isActive: boolean): Promise<IUser | null>;
  updateProfile(id: string, profileData: Partial<IUser>): Promise<IUser | null>;
  updatePrivacySettings(id: string, showAnswers: boolean, showProfile: boolean): Promise<IUser | null>;
}

export class UserRepository extends BaseRepository<IUser> implements IUserRepository {
  constructor() {
    super(User);
  }

  async findByEmail(email: string): Promise<IUser | null> {
    return this.findOne({ email: email.toLowerCase() });
  }

  async updateLastLogin(id: string): Promise<IUser | null> {
    return this.update(id, { lastLogin: new Date() });
  }

  async setActive(id: string, isActive: boolean): Promise<IUser | null> {
    return this.update(id, { isActive });
  }

  async updateProfile(id: string, profileData: Partial<IUser>): Promise<IUser | null> {
    // Filter out sensitive fields that shouldn't be updated through this method
    const { password, email, isActive, ...safeData } = profileData;
    return this.update(id, safeData);
  }

  async updatePrivacySettings(id: string, showAnswers: boolean, showProfile: boolean): Promise<IUser | null> {
    return this.update(id, {
      'preferences.privacySettings.showAnswers': showAnswers,
      'preferences.privacySettings.showProfile': showProfile
    });
  }
}

// Export a singleton instance
export const userRepository = new UserRepository();