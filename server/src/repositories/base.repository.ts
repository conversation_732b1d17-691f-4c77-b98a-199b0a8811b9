import { Document, FilterQuery, Model, UpdateQuery } from 'mongoose';

// Generic base repository interface defining common CRUD operations
export interface IBaseRepository<T extends Document> {
  findAll(): Promise<T[]>;
  findById(id: string, populate?: string[]): Promise<T | null>;
  findOne(filterQuery: FilterQuery<T>): Promise<T | null>;
  find(filterQuery: FilterQuery<T>): Promise<T[]>;
  create(item: Partial<T>): Promise<T>;
  update(id: string, item: UpdateQuery<T>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
}

// Base repository implementation that can be extended by specific repositories
export abstract class BaseRepository<T extends Document> implements IBaseRepository<T> {
  constructor(protected readonly model: Model<T>) {}

  async findAll(): Promise<T[]> {
    return this.model.find();
  }

  async findById(id: string, populate?: string[]): Promise<T | null> {
    if (!populate) return this.model.findById(id);
    return this.model.findById(id).populate(populate);
  }

  async findOne(filterQuery: FilterQuery<T>): Promise<T | null> {
    return this.model.findOne(filterQuery);
  }

  async find(filterQuery: FilterQuery<T>): Promise<T[]> {
    return this.model.find(filterQuery);
  }

  async create(item: Partial<T>): Promise<T> {
    return this.model.create(item);
  }

  async update(id: string, item: UpdateQuery<T>): Promise<T | null> {
    return this.model.findByIdAndUpdate(id, item, { new: true });
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.model.findByIdAndDelete(id);
    return result !== null;
  }
}