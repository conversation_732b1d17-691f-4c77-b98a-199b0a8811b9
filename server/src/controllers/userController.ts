import { Request, Response, NextFunction } from 'express';
import { UserService, RelationshipService } from '../services';
import { AppError } from '../middleware/errorHandler';
import mongoose from 'mongoose';

export const userController = {
  // Register a new user
  register: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { firstName, lastName, email, password } = req.body;
      
      if (!firstName || !lastName || !email || !password) {
        throw new AppError('First name, last name, email, and password are required', 400);
      }
      
      // Register user using the user service
      const { user, token } = await UserService.register({
        firstName,
        lastName,
        email,
        password
      });
      
      res.status(201).json({
        status: 'success',
        data: {
          user,
          token
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  // Login user
  login: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password } = req.body;
      
      if (!email || !password) {
        throw new AppError('Email and password are required', 400);
      }
      
      // Login using the user service
      const { user, token } = await UserService.login(email, password);
      
      // Get active relationship if exists
      const relationship = await RelationshipService.getActiveRelationship(user._id.toString());
      
      // Include partner info in response
      const responseUser = {
        ...user,
        partner: relationship ? {
          id: relationship.userOne.toString() === user._id.toString() 
            ? relationship.userTwo._id 
            : relationship.userOne._id,
          status: relationship.status
        } : null
      };
      
      res.status(200).json({
        status: 'success',
        data: {
          user: responseUser,
          token
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  // Get current user profile
  getProfile: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user.id;
      
      // Get user profile
      const user = await UserService.getProfile(userId);
      
      // Get active relationship if exists
      const relationship = await RelationshipService.getActiveRelationship(userId);
      
      // Include partner info in response
      const responseUser = {
        ...user,
        partner: relationship ? {
          id: relationship.userOne._id.toString() === userId.toString() 
            ? relationship.userTwo._id 
            : relationship.userOne._id,
          status: relationship.status
        } : null
      };
      
      res.status(200).json({
        status: 'success',
        data: {
          user: responseUser,
          relationship
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  // Update user profile
  updateProfile: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { firstName, lastName, bio } = req.body;
      const userId = req.user.id;
      
      const updatedProfile = {
        ...(firstName && { firstName }),
        ...(lastName && { lastName }),
        ...(bio && { bio })
      };
      
      // Update user profile
      const user = await UserService.updateProfile(userId, updatedProfile);
      
      // Get active relationship if exists
      const relationship = await RelationshipService.getActiveRelationship(userId);
      
      // Include partner info in response
      const responseUser = {
        ...user,
        partner: relationship ? {
          id: relationship.userOne._id.toString() === userId.toString() 
            ? relationship.userTwo._id 
            : relationship.userOne._id,
          status: relationship.status
        } : null
      };
      
      res.status(200).json({
        status: 'success',
        data: {
          user: responseUser
        }
      });
    } catch (error) {
      next(error);
    }
  },
  
  // Update user privacy settings
  updatePrivacySettings: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { showAnswers, showProfile } = req.body;
      const userId = req.user.id;
      
      if (typeof showAnswers !== 'boolean' || typeof showProfile !== 'boolean') {
        throw new AppError('Invalid privacy settings', 400);
      }
      
      // Update privacy settings
      const user = await UserService.updatePrivacySettings(userId, {
        showAnswers,
        showProfile
      });
      
      res.status(200).json({
        status: 'success',
        data: {
          user
        }
      });
    } catch (error) {
      next(error);
    }
  },

  // Send a relationship request to another user by email
  sendRelationshipRequest: async (req: Request, res: Response) => {
    try {
      const { email } = req.body;
      const userId = req.user.id;

      if (!email) {
        res.status(400).json({ message: 'Email is required' });
        return;
      }

      const result = await RelationshipService.requestRelationshipByEmail(userId, email);
      
      if (result.message) {
        res.status(400).json({ message: result.message });
        return;
      }

      res.status(201).json({
        data : { 
          message: 'Relationship request sent successfully', 
          relationship: result.relationship 
        }
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // Get pending relationships (both incoming and outgoing)
  getPendingRelationships: async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      
      const hasActiveRelationship = await RelationshipService.getActiveRelationship(userId);

      if (hasActiveRelationship) {
        res.status(203).json({ data: hasActiveRelationship });
        return;
      }

      const pendingRelationships = await RelationshipService.getAllPendingRequests(userId);
      const declinedRelationships = await RelationshipService.getDeclinedRequests(userId);
      
      res.status(200).json({ data : { ...pendingRelationships, ...declinedRelationships } });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // Accept a relationship request
  acceptRelationshipRequest: async (req: Request, res: Response) => {
    try {
      const { relationshipId } = req.body;
      const userId = req.user.id;
      
      if (!relationshipId || !mongoose.Types.ObjectId.isValid(relationshipId)) {
        res.status(400).json({ message: 'Valid relationship ID is required' });
        return;
      }

      const relationship = await RelationshipService.acceptRelationship(userId, relationshipId);
      
      res.status(200).json({
        data : { 
          message: 'Relationship request accepted', 
          relationship 
        }
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  },

  // Decline a relationship request
  declineRelationshipRequest: async (req: Request, res: Response) => {
    try {
      const { relationshipId } = req.params;
      const userId = req.user.id;
      
      if (!relationshipId || !mongoose.Types.ObjectId.isValid(relationshipId)) {
        res.status(400).json({ message: 'Valid relationship ID is required' });
        return;
      }

      const relationship = await RelationshipService.declineRelationship(userId, relationshipId);
      
      res.status(200).json({
        data : { 
          message: 'Relationship request declined', 
          relationship 
        }
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  },

  // Cancel an outgoing relationship request
  cancelRelationshipRequest: async (req: Request, res: Response) => {
    try {
      const { relationshipId } = req.params;
      const userId = req.user.id;
      
      if (!relationshipId || !mongoose.Types.ObjectId.isValid(relationshipId)) {
        res.status(400).json({ message: 'Valid relationship ID is required' });
        return;
      }

      await RelationshipService.cancelRequest(userId, relationshipId);
      
      res.status(200).json({
        data : { 
          message: 'Relationship request canceled' 
        }
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  },
  
  // Update timezone preferences for an active relationship
  updateTimezonePreferences: async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      const { timezone, preferredQuestionTime } = req.body;
      
      if (!timezone || !preferredQuestionTime) {
        res.status(400).json({ message: 'Timezone and preferred question time are required' });
        return;
      }
      
      // Get the user's active relationship
      const relationship = await RelationshipService.getActiveRelationship(userId);
      
      if (!relationship) {
        res.status(404).json({ message: 'No active relationship found' });
        return;
      }
      
      // Update the timezone preferences
      const updatedRelationship = await RelationshipService.updateTimezonePreferences(
        relationship._id.toString(),
        timezone,
        preferredQuestionTime
      );
      
      res.status(200).json({
        data: {
          message: 'Timezone preferences updated successfully',
          relationship: updatedRelationship
        }
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
};