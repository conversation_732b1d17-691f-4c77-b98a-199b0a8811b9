import { userRepository } from '../../repositories';
import { IUser } from '../../models';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';

// Secret key for JWT token generation - in production this should be in env vars
const JWT_SECRET = process.env.JWT_SECRET || 'novan-secret-key';

export class UserService {
  /**
   * Register a new user
   */
  static async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }): Promise<{ user: Partial<IUser>; token: string }> {
    // Check if user already exists
    const existingUser = await userRepository.findByEmail(userData.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash the password
    const hashedPassword = await this.hashPassword(userData.password);

    // Create the new user
    const user = await userRepository.create({
      ...userData,
      password: hashedPassword,
      isActive: true,
      preferences: {
        emailNotifications: true,
        privacySettings: {
          showAnswers: true,
          showProfile: true,
        },
      },
      lastLogin: new Date(),
    });

    // Generate JWT token
    const token = this.generateToken(user);

    // Return user (without password) and token
    const { password, ...userWithoutPassword } = user.toObject();
    return { user: userWithoutPassword, token };
  }

  /**
   * Login a user
   */
  static async login(email: string, password: string): Promise<{ user: Partial<IUser>; token: string }> {
    // Find user by email
    const user = await userRepository.findByEmail(email);
    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Check if password matches
    const isPasswordValid = await this.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      throw new Error('Invalid credentials');
    }

    // Update last login timestamp
    await userRepository.updateLastLogin(user._id.toString());

    // Generate JWT token
    const token = this.generateToken(user);

    // Return user (without password) and token
    const { password: _, ...userWithoutPassword } = user.toObject();
    return { user: userWithoutPassword, token };
  }

  /**
   * Get user profile by ID
   */
  static async getProfile(userId: string): Promise<Partial<IUser>> {
    const user = await userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Return user without sensitive information
    const { password, ...userWithoutPassword } = user.toObject();
    return userWithoutPassword;
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, profileData: Partial<IUser>): Promise<Partial<IUser>> {
    const updatedUser = await userRepository.updateProfile(userId, profileData);
    if (!updatedUser) {
      throw new Error('Failed to update profile');
    }

    // Return user without sensitive information
    const { password, ...userWithoutPassword } = updatedUser.toObject();
    return userWithoutPassword;
  }

  /**
   * Update user privacy settings
   */
  static async updatePrivacySettings(
    userId: string,
    settings: { showAnswers: boolean; showProfile: boolean }
  ): Promise<Partial<IUser>> {
    const updatedUser = await userRepository.updatePrivacySettings(
      userId,
      settings.showAnswers,
      settings.showProfile
    );

    if (!updatedUser) {
      throw new Error('Failed to update privacy settings');
    }

    // Return user without sensitive information
    const { password, ...userWithoutPassword } = updatedUser.toObject();
    return userWithoutPassword;
  }

  /**
   * Hash a password
   */
  private static async hashPassword(password: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const salt = crypto.randomBytes(16).toString('hex');
      crypto.scrypt(password, salt, 64, (err, derivedKey) => {
        if (err) reject(err);
        resolve(salt + ':' + derivedKey.toString('hex'));
      });
    });
  }

  /**
   * Verify a password against a hash
   */
  private static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const [salt, key] = hash.split(':');
      crypto.scrypt(password, salt, 64, (err, derivedKey) => {
        if (err) reject(err);
        resolve(key === derivedKey.toString('hex'));
      });
    });
  }

  /**
   * Generate a JWT token for a user
   */
  private static generateToken(user: IUser): string {
    return jwt.sign(
      {
        id: user._id,
        email: user.email,
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
  }
}