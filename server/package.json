{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc -p .", "test": "echo \"Error: no test specified\" && exit 1", "seed": "npx ts-node src/utils/seed/seedAll.ts", "seed:questions": "npx ts-node src/utils/seed/seedQuestions.ts", "seed:users": "npx ts-node src/utils/seed/seedUsers.ts", "seed:relationships": "npx ts-node src/utils/seed/seedRelationships.ts", "seed:answers": "npx ts-node src/utils/seed/seedAnswers.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.3", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}