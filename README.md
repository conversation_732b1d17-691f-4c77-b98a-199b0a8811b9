# Novan - Daily Questions for Deeper Connections

Novan is a relationship-building platform that strengthens connections between partners through daily guided questions. The app delivers thought-provoking questions each day, creating opportunities for meaningful conversations and deeper understanding between partners.

## 🚀 Core Features

- **Daily Questions**: Partners receive a new thought-provoking question daily
- **Private Answer Sharing**: Answers remain hidden until both partners respond
- **Question History**: Review past questions and answers to track your journey together
- **Timezone Management**: Customized question delivery based on your timezone preferences
- **Relationship Insights**: Track your interaction patterns and engagement over time

## 💻 Technical Stack

### Backend
- **Node.js & Express**: Modern TypeScript-based REST API 
- **MongoDB**: NoSQL database with Mongoose ODM
- **JWT Authentication**: Secure user authentication and authorization
- **Cron Jobs**: Scheduled question delivery system

### Frontend
- **Next.js**: React framework with App Router for optimized page rendering
- **Redux Toolkit**: State management for the application
- **TailwindCSS**: Utility-first CSS framework for responsive design
- **Three.js**: 3D visualization components for relationship insights

## 🏗️ Project Structure

```
novan/
├── server/             # Express backend
│   ├── src/            # TypeScript source files
│   │   ├── config/     # Environment variables and configuration
│   │   ├── controllers/ # Route controllers
│   │   ├── middleware/ # Express middleware (authentication, error handling)
│   │   ├── models/     # MongoDB schema models
│   │   ├── routes/     # API route definitions
│   │   ├── services/   # Business logic layer
│   │   ├── repositories/ # Data access layer
│   │   ├── types/      # TypeScript type definitions
│   │   └── utils/      # Utility functions and helpers
│
└── web/               # Next.js frontend
    ├── src/
    │   ├── app/       # Next.js App Router pages
    │   ├── components/ # React components
    │   ├── store/     # Redux store configuration
    │   ├── types/     # TypeScript type definitions
    │   └── utils/     # Frontend utilities
    └── public/        # Static assets
```

## 🚀 Getting Started

### Prerequisites
- Node.js v16+
- MongoDB
- npm or yarn

### Backend Setup

```bash
# Navigate to server directory
cd server

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your MongoDB connection string and JWT secret

# Start development server
npm run dev
```

The Express server will run on http://localhost:5000 by default.

### Frontend Setup

```bash
# Navigate to web directory
cd web

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your API endpoint

# Start development server
npm run dev
```

The Next.js application will be available at http://localhost:3000.

## 📋 Key Concepts

### Question Assignment System
Novan intelligently assigns daily questions from different categories to ensure variety and engagement:
- Questions rotate through categories: lighthearted, thoughtful, personal, and future-oriented
- No question is repeated until all questions in the pool have been used
- Partners always receive the same question on the same day

### Answer Visibility Rules
- By default, answers remain hidden until both partners respond
- Optional settings allow for immediate visibility or time-based revealing
- Past answers are always accessible to both partners through the history view

## 👨‍💻 Development Roadmap

- ✅ Foundation setup with basic frontend/backend communication
- ✅ Core question and relationship functionality
- ✅ MongoDB integration for persistent data storage
- ✅ User authentication and authorization
- ⏳ Enhanced notifications system with email and push notifications
- ⏳ Rich media uploads for answers (photos, videos)
- ⏳ Analytics dashboard for relationship insights
- ⏳ Mobile application development

## 📄 License

[MIT](LICENSE)