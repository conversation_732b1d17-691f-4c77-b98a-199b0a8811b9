/**
 * PWA Configuration Validator
 * 
 * This script checks your PWA configuration for common issues.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Novan PWA Configuration Checker\n');

// Paths to check
const paths = {
  manifest: path.join(__dirname, '..', 'public', 'manifest.json'),
  serviceWorker: path.join(__dirname, '..', 'public', 'sw.js'),
  serviceWorkerReg: path.join(__dirname, '..', 'public', 'sw-register.js'),
  nextConfig: path.join(__dirname, '..', 'next.config.ts'),
  iconsDir: path.join(__dirname, '..', 'public', 'icons'),
  screenshotsDir: path.join(__dirname, '..', 'public', 'screenshots'),
  offlinePage: path.join(__dirname, '..', 'public', 'offline.html')
};

// Check file existence
function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  console.log(`${status} ${description}: ${path.basename(filePath)}`);
  return exists;
}

// Check directory contents
function checkDirectoryContents(dirPath, description, expectedCount = 1) {
  if (!fs.existsSync(dirPath)) {
    console.log(`❌ ${description}: Directory not found`);
    return false;
  }
  
  const files = fs.readdirSync(dirPath);
  const status = files.length >= expectedCount ? '✅' : '⚠️';
  console.log(`${status} ${description}: ${files.length} files found`);
  return files.length >= expectedCount;
}

// Check JSON field
function checkJsonField(filePath, field, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const json = JSON.parse(content);
    
    const hasField = json[field] !== undefined;
    const status = hasField ? '✅' : '❌';
    console.log(`${status} ${description}: "${field}" field`);
    return hasField;
  } catch (err) {
    console.log(`❌ Error checking ${description}: ${err.message}`);
    return false;
  }
}

// Run checks
console.log('Checking file existence:');
checkFileExists(paths.manifest, 'Web App Manifest');
checkFileExists(paths.serviceWorker, 'Service Worker');
checkFileExists(paths.serviceWorkerReg, 'Service Worker Registration');
checkFileExists(paths.offlinePage, 'Offline Page');
checkFileExists(paths.nextConfig, 'Next.js Configuration');

console.log('\nChecking directories:');
checkDirectoryContents(paths.iconsDir, 'PWA Icons', 8);
checkDirectoryContents(paths.screenshotsDir, 'PWA Screenshots', 2);

console.log('\nChecking manifest.json:');
checkJsonField(paths.manifest, 'name', 'Manifest');
checkJsonField(paths.manifest, 'short_name', 'Manifest');
checkJsonField(paths.manifest, 'start_url', 'Manifest');
checkJsonField(paths.manifest, 'icons', 'Manifest');
checkJsonField(paths.manifest, 'display', 'Manifest');

console.log('\nChecking package.json dependencies:');
try {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const hasNextPWA = packageJson.dependencies['next-pwa'] !== undefined;
  console.log(`${hasNextPWA ? '✅' : '❌'} next-pwa dependency`);
} catch (err) {
  console.log(`❌ Error checking package.json: ${err.message}`);
}

console.log('\nPWA Configuration Check Complete!\n');
console.log('For a complete PWA audit, run:');
console.log('1. Build your application: npm run build:pwa');
console.log('2. Start the production server: npm start');
console.log('3. Run Lighthouse audit in Chrome DevTools on the PWA category');
