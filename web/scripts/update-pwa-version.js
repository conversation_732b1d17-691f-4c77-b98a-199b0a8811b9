/**
 * Update PWA Version Script
 * 
 * This script updates the cache version in the service worker file
 * to ensure PWA users get the latest version of the application.
 */

const fs = require('fs');
const path = require('path');

// Paths
const serviceWorkerPath = path.join(__dirname, '..', 'public', 'sw.js');

// Read the current service worker file
let swContent;
try {
  swContent = fs.readFileSync(serviceWorkerPath, 'utf8');
} catch (err) {
  console.error('Error reading service worker file:', err.message);
  process.exit(1);
}

// Generate a new cache version with current timestamp
const newCacheVersion = `novan-cache-v${Date.now()}`;

// Replace the cache version
const updatedContent = swContent.replace(
  /const CACHE_NAME = ['"]([^'"]+)['"]/,
  `const CACHE_NAME = '${newCacheVersion}'`
);

// Write back the updated content
try {
  fs.writeFileSync(serviceWorkerPath, updatedContent);
  console.log(`✅ Successfully updated PWA cache version to: ${newCacheVersion}`);
} catch (err) {
  console.error('Error writing updated service worker file:', err.message);
  process.exit(1);
}

// Also update the sw-register.js file with a version parameter to force refresh
const registerPath = path.join(__dirname, '..', 'public', 'sw-register.js');

let registerContent;
try {
  registerContent = fs.readFileSync(registerPath, 'utf8');
} catch (err) {
  console.error('Error reading service worker registration file:', err.message);
  process.exit(1);
}

// Add a version parameter to the service worker URL
const updatedRegisterContent = registerContent.replace(
  /navigator\.serviceWorker\.register\(['"]([^'"]+)['"]\)/,
  `navigator.serviceWorker.register('$1?v=${Date.now()}')`
);

// Write back the updated content
try {
  fs.writeFileSync(registerPath, updatedRegisterContent);
  console.log(`✅ Added version parameter to service worker registration`);
} catch (err) {
  console.error('Error writing updated service worker registration file:', err.message);
  process.exit(1);
}
