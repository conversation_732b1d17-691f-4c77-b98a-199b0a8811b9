import fs from 'fs';

const screenshots = [
  { name: 'dashboard', width: 1280, height: 720 },
  { name: 'mobile', width: 720, height: 1280 }
];
const directory = './public/screenshots';

// Create the directory if it doesn't exist
if (!fs.existsSync(directory)) {
  fs.mkdirSync(directory, { recursive: true });
}

// Create empty files for screenshots
screenshots.forEach(screenshot => {
  const filePath = `${directory}/${screenshot.name}.png`;
  fs.writeFileSync(filePath, '');
  console.log(`Created placeholder screenshot: ${filePath} (${screenshot.width}x${screenshot.height})`);
});
