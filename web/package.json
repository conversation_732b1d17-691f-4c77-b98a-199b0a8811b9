{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.3", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "framer-motion": "^12.9.2", "gsap": "^3.13.0", "js-cookie": "^3.0.5", "lil-gui": "^0.20.0", "lottie-web": "^5.12.2", "next": "15.3.1", "next-cookie": "^2.8.0", "particles.js": "^2.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "three": "^0.176.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.176.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4.1.7", "typescript": "^5"}}