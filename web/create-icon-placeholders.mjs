import fs from 'fs';

const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
const directory = './public/icons';

// Create the directory if it doesn't exist
if (!fs.existsSync(directory)) {
  fs.mkdirSync(directory, { recursive: true });
}

// Create empty files for each size
sizes.forEach(size => {
  const filePath = `${directory}/icon-${size}x${size}.png`;
  fs.writeFileSync(filePath, '');
  console.log(`Created placeholder file: ${filePath}`);
});
