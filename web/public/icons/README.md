# Novan PWA Icons

This directory should contain icons for the Progressive Web App (PWA) functionality.

## Required Icon Files

Please generate the following icon files from your logo or branding elements:

- `icon-72x72.png` (72x72 pixels)
- `icon-96x96.png` (96x96 pixels)
- `icon-128x128.png` (128x128 pixels)
- `icon-144x144.png` (144x144 pixels)
- `icon-152x152.png` (152x152 pixels)
- `icon-192x192.png` (192x192 pixels)
- `icon-384x384.png` (384x384 pixels)
- `icon-512x512.png` (512x512 pixels)

You can use your existing assets like:
- The heart cluster from `/public/images/features/heart-cluster.svg`
- Or other suitable branding elements

## Icon Generation Tools

You can use online tools like:
1. [PWA Asset Generator](https://www.npmjs.com/package/pwa-asset-generator)
2. [Real Favicon Generator](https://realfavicongenerator.net/)
3. [Favicon.io](https://favicon.io/)

## Directory Structure

Place all icons in the `/public/icons/` directory.

## Screenshots

Also create screenshots for app stores:
- `/public/screenshots/dashboard.png` (1280x720)
- `/public/screenshots/mobile.png` (720x1280)
