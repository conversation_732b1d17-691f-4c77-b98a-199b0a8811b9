// Service Worker for Novan PWA
const CACHE_NAME = 'novan-cache-v1';
const OFFLINE_PAGE = '/offline.html';

// Assets to cache immediately upon installation
const urlsToCache = [
  '/',
  '/dashboard',
  '/manifest.json',
  OFFLINE_PAGE,
  
  // App assets
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/screenshots/dashboard.png',
  
  // Font files
  '/fonts/HeartLove.ttf',
  '/fonts/SuperShiny.ttf',
  
  // Critical CSS and JS
  '/globals.css'
];

self.addEventListener('install', function(event) {
  // Perform install steps
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', function(event) {
  // Skip cross-origin requests
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          // If navigate fails (offline), show offline page
          return caches.match(OFFLINE_PAGE);
        })
    );
    return;
  }
  
  // Standard cache strategy for non-navigation requests
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Cache hit - return response
        if (response) {
          return response;
        }
        
        // Clone the request - request streams can only be used once
        const fetchRequest = event.request.clone();
        
        return fetch(fetchRequest)
          .then(function(response) {
            // Check if we received a valid response
            if(!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Cache the fetched response
            caches.open(CACHE_NAME)
              .then(function(cache) {
                cache.put(event.request, responseToCache);
              });

            return response;
          })
          .catch(function() {
            // For image requests, return a placeholder if offline
            if (event.request.destination === 'image') {
              return caches.match('/icons/icon-192x192.png');
            }
            
            // For other resources, just fail
            return new Response('Network error happened', {
              status: 408,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});

self.addEventListener('activate', function(event) {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
