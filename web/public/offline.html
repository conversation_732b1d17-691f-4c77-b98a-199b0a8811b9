<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="theme-color" content="#3b82f6">
  <title>Offline - Novan</title>
  <style>
    :root {
      --primary-color: #3b82f6;
      --background-color: #ffffff;
      --text-color: #111827;
      --error-color: #ef4444;
    }
    
    @media (prefers-color-scheme: dark) {
      :root {
        --primary-color: #60a5fa;
        --background-color: #1f2937;
        --text-color: #f9fafb;
        --error-color: #f87171;
      }
    }
    
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background-color: var(--background-color);
      color: var(--text-color);
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
    }
    
    .container {
      width: 90%;
      max-width: 500px;
      padding: 2rem;
    }
    
    h1 {
      margin-top: 0;
      font-size: 1.8rem;
    }
    
    p {
      line-height: 1.6;
      margin-bottom: 2rem;
    }
    
    .heart-icon {
      height: 80px;
      width: 80px;
      margin-bottom: 2rem;
      color: var(--error-color);
    }
    
    .retry-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 0.75rem 2rem;
      border-radius: 0.5rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s;
      font-size: 1rem;
    }
    
    .retry-button:hover {
      background-color: #2563eb;
    }
  </style>
</head>
<body>
  <div class="container">
    <svg class="heart-icon" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
    </svg>
    <h1>You're Offline</h1>
    <p>It looks like you're currently offline. To continue using Novan, please reconnect to the internet.</p>
    <button class="retry-button" onclick="window.location.reload()">Try Again</button>
  </div>
  
  <script>
    // Check if online status changes
    window.addEventListener('online', () => {
      window.location.href = '/dashboard';
    });
  </script>
</body>
</html>
