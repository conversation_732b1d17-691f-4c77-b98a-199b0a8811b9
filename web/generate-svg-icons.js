// Simple PWA Icon Generator using SVG
import fs from 'fs';
import path from 'path';

// Get heart cluster SVG content - assumed to be in public/images/features/heart-cluster.svg
const svgPath = path.join(__dirname, 'public', 'images', 'features', 'heart-cluster.svg');
let svgContent;

try {
  svgContent = fs.readFileSync(svgPath, 'utf8');
  console.log('Found heart-cluster.svg, will use it as base for PWA icons');
} catch (err) {
  console.error('Error reading heart-cluster.svg:', err.message);
  console.log('Will generate simple placeholder icons instead');
  svgContent = `
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <rect width="100" height="100" fill="#ef4444" />
  <path d="M50,30 C60,20 80,20 80,40 C80,60 60,70 50,85 C40,70 20,60 20,40 C20,20 40,20 50,30 Z" fill="white" />
  <text x="50" y="95" font-family="Arial" font-size="12" text-anchor="middle" fill="white">Novan</text>
</svg>
  `.trim();
}

// Define icon sizes
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
const outputDir = path.join(__dirname, 'public', 'icons');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to create SVG content with custom size
function createSvgWithSize(size) {
  // Extract the SVG content but modify width and height attributes
  let customSvg = svgContent
    .replace(/width="([^"]+)"/, `width="${size}"`)
    .replace(/height="([^"]+)"/, `height="${size}"`)
    .replace(/viewBox="([^"]+)"/, `viewBox="0 0 800 450"`);
  
  return customSvg;
}

// Generate SVG files for each size
sizes.forEach(size => {
  const svg = createSvgWithSize(size);
  const svgFilePath = path.join(outputDir, `icon-${size}x${size}.svg`);
  
  fs.writeFileSync(svgFilePath, svg);
  console.log(`Generated: ${svgFilePath}`);
});

console.log('\nSVG icons have been generated successfully.');
console.log('\nIMPORTANT: For best results, you should convert these SVGs to PNG format.');
console.log('You can use online tools like:');
console.log('- https://cloudconvert.com/svg-to-png');
console.log('- https://svgtopng.com/');
console.log('\nThen replace the SVG files with the PNG files, keeping the same filenames but with .png extension.');
