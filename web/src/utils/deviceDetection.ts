// Utility functions for device detection and mobile optimization

export function isMobileDevice() {
  if (typeof window === 'undefined') return false;
  
  // Check for touch capability as a proxy for mobile devices
  const hasTouchScreen = !!(
    ('ontouchstart' in window) ||
    (window.navigator.maxTouchPoints > 0)
  );
  
  // Check screen width
  const isSmallScreen = window.innerWidth <= 768;
  
  // Additional check for mobile user agents
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobileUserAgent = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
  
  return (hasTouchScreen && isSmallScreen) || isMobileUserAgent;
}

export function getLowPerformanceConfig() {
  return {
    particles: {
      number: { value: 30 }, // Significantly reduce particle count
      opacity: { value: 0.3 },
      size: { value: 4 },
      move: { speed: 2 }
    },
    disableInteractivity: false
  };
}
