import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import { getAuthToken, removeAuthToken, setAuthToken } from "./cookies";
import {
  ApiError,
  ApiResponse,
  RegisterRequest,
  RegisterResponse,
  LoginRequest,
  LoginResponse,
  User,
  DailyQuestion,
  SubmitAnswerResponse,
  GetHistoryResponse,
  SendRequestResponse,
  PendingRelationshipsResponse,
  AcceptRelationshipRequest,
  DeclineRelationshipRequest,
  CancelRelationshipRequest,
  Relationship,
} from "@/types/api";

/**
 * Class for handling API errors with proper typing
 */
export class ApiClientError extends Error implements ApiError {
  status: number;
  details?: unknown;

  constructor(status: number, message: string, details?: unknown) {
    super(message);
    this.name = "ApiClientError";
    this.status = status;
    this.details = details;
  }
}

/**
 * API Client with proper error handling and TypeScript support
 */
class ApiClient {
  private client: AxiosInstance;

  constructor() {
    // Create Axios instance with default configuration
    // Using Next.js API routes to proxy requests
    this.client = axios.create({
      baseURL: "/api", // Next.js API routes
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Add request interceptor for authentication
    this.client.interceptors.request.use(
      (config) => {
        // Add authorization token to headers if it exists and in browser environment
        if (typeof window !== "undefined") {
          const token = getAuthToken();
          if (token && config.headers) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }
        return config;
      },
      (error) => Promise.reject(this.handleError(error))
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => Promise.reject(this.handleError(error))
    );
  }

  /**
   * Helper method to handle API errors
   */
  private handleError(error: unknown): ApiClientError {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<{ error?: string }>;
      const status = axiosError.response?.status || 500;
      const message =
        axiosError.response?.data?.error ||
        axiosError.message ||
        "An unexpected error occurred";

      // Log error details
      console.error(`API Error (${status}):`, message);

      // Force logout on authentication errors
      if (status === 401 && typeof window !== "undefined") {
        removeAuthToken();
        // Redirect to login page if not already there
        if (
          window.location.pathname !== "/login" &&
          window.location.pathname !== "/"
        ) {
          window.location.href = "/login";
        }
      }

      return new ApiClientError(status, message, axiosError.response?.data);
    }

    // Handle non-Axios errors
    return new ApiClientError(500, String(error) || "Unknown error", error);
  }

  // Generic request method with TypeScript generics
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client(config);
      console.log("api request response", response);
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Health check
  async checkHealth(): Promise<{ status: string }> {
    const response = await this.request<{ status: string }>({
      url: "/health",
      method: "GET",
    });
    return response;
  }

  // Authentication APIs
  async register({
    firstName,
    lastName,
    email,
    password,
  }: RegisterRequest): Promise<RegisterResponse> {
    const response = await this.request<RegisterResponse>({
      url: "/users/register",
      method: "POST",
      data: { firstName, lastName, email, password },
    });

    if (response.token) {
      setAuthToken(response.token);
    }

    return response;
  }

  async login({ email, password }: LoginRequest): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>({
      url: "/users/login",
      method: "POST",
      data: { email, password },
    });

    if (response.token) {
      setAuthToken(response.token);
    }

    return response;
  }

  logout(): void {
    removeAuthToken();
  }

  // User APIs
  async getUserProfile(): Promise<{ user: User, relationship: Relationship }> {
    return this.request<{ user: User, relationship: Relationship }>({
      url: "/users/profile",
      method: "GET",
    });
  }

  // Update user profile
  async updateUserProfile(
    firstName: string,
    lastName: string,
    bio: string
  ): Promise<{ user: User }> {
    return this.request<{ user: User }>({
      url: "/users/profile",
      method: "PUT",
      data: { firstName, lastName, bio },
    });
  }

  async updatePrivacySettings(
    showAnswers: boolean,
    showProfile: boolean
  ): Promise<{ user: User }> {
    return this.request<{ user: User }>({
      url: "/users/privacy-settings",
      method: "PUT",
      data: { showAnswers, showProfile },
    });
  }

  // Question APIs
  async getDailyQuestion() {
    return this.request<{ question: DailyQuestion }>({
      url: "/questions/daily",
      method: "GET",
    });
  }

  async submitAnswer(answer: string) {
    return this.request<SubmitAnswerResponse>({
      url: "/questions/answer",
      method: "POST",
      data: { answer },
    });
  }

  async getAnswerHistory(month?: number, year?: number) {
    return this.request<GetHistoryResponse>({
      url: "/questions/history",
      method: "GET",
      params: { month, year },
    });
  }

  // Relationship APIs
  async sendRelationshipRequest(email: string) {
    const res = await this.request<SendRequestResponse>({
      url: "/users/relationship/request",
      method: "POST",
      data: { email },
    });
    return res;
  }

  async updateTimezonePreferences(timezone: string, preferredQuestionTime: string) {
    return this.request<{ message: string, relationship: Relationship }>({
      url: "/users/relationship/timezone",
      method: "PUT",
      data: { timezone, preferredQuestionTime },
    });
  }

  async getPendingRelationships(): Promise<
    PendingRelationshipsResponse | Relationship
  > {
    return this.request<PendingRelationshipsResponse | Relationship>({
      url: "/users/relationship/pending",
      method: "GET",
    });
  }

  async acceptRelationshipRequest(
    relationshipId: string
  ): Promise<AcceptRelationshipRequest> {
    return this.request<AcceptRelationshipRequest>({
      url: `/users/relationship/${relationshipId}/accept`,
      method: "POST",
      data: { relationshipId },
    });
  }

  async declineRelationshipRequest(
    relationshipId: string
  ): Promise<DeclineRelationshipRequest> {
    return this.request<DeclineRelationshipRequest>({
      url: `/users/relationship/${relationshipId}/decline`,
      method: "POST",
      data: { relationshipId },
    });
  }

  async cancelRelationshipRequest(
    relationshipId: string
  ): Promise<CancelRelationshipRequest> {
    return this.request<CancelRelationshipRequest>({
      url: `/users/relationship/${relationshipId}/cancel`,
      method: "DELETE",
    });
  }
}

// Create and export a singleton instance
const api = new ApiClient();

export default api;

// Re-export individual API methods for convenience with proper binding
export const checkHealth = api.checkHealth.bind(api);
export const register = api.register.bind(api);
export const login = api.login.bind(api);
export const logout = api.logout.bind(api);
export const getUserProfile = api.getUserProfile.bind(api);
export const updateUserProfile = api.updateUserProfile.bind(api);
export const updatePrivacySettings = api.updatePrivacySettings.bind(api);
export const getDailyQuestion = api.getDailyQuestion.bind(api);
export const submitAnswer = api.submitAnswer.bind(api);
export const getAnswerHistory = api.getAnswerHistory.bind(api);

// Relationship API methods
export const sendRelationshipRequest = api.sendRelationshipRequest.bind(api);
export const updateTimezonePreferences = api.updateTimezonePreferences.bind(api);
export const getPendingRelationships = api.getPendingRelationships.bind(api);
export const acceptRelationshipRequest =
  api.acceptRelationshipRequest.bind(api);
export const declineRelationshipRequest =
  api.declineRelationshipRequest.bind(api);
export const cancelRelationshipRequest =
  api.cancelRelationshipRequest.bind(api);
