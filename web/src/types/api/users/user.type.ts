// User related types
export interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  name?: string; // Computed from firstName + lastName
  isActive: boolean;
  bio?: string;
  partnerId: string | null;
  partner?: {
    id: string;
    status: "active" | "paused" | "ended";
  };
  createdAt: string;
  updatedAt: string;
  preferences?: {
    emailNotifications: boolean;
    privacySettings: {
      showAnswers: boolean;
      showProfile: boolean;
    };
  };
}
