import { User } from "../users/user.type";

// Relationship related types
export interface RelationshipAnswer {
  userId: string;
  content: string;
  submittedAt: string;
}

export interface AskedQuestion {
  questionId: string;
  dateAssigned: string;
  answers: RelationshipAnswer[];
}

export interface Relationship {
  _id: string;
  userOne: User;
  userTwo: User;
  status: "pending" | "active" | "blocked" | "declined";
  createdAt: string;
  updatedAt: string;
  activatedAt?: string;
  lastInteractionDate?: string;
  askedQuestions: AskedQuestion[];
  metadata: {
    initiator: string;
    questionStreak?: number;
    totalInteractions?: number;
    compatibilityScore?: number;
    notes?: string;
    timezone?: string;
    preferredQuestionTime?: string;
  };
}

export interface SendRequestResponse {
  message: string;
  relationship?: Relationship;
}

export interface PendingRelationshipsResponse {
  incoming: Relationship[];
  outgoing: Relationship[];
}

export interface AcceptRelationshipRequest {
  message: string;
  relationship: Relationship;
}

export interface DeclineRelationshipRequest {
  message: string;
  relationship: Relationship;
}

export interface CancelRelationshipRequest {
  message: string;
}
