export interface HistoryEntry {
    date: string;
    question: {
      id: string;
      text: string;
      category: string;
      inputType: string;
    };
    userAnswer: string | null;
    partnerAnswer: string | null;
    bothAnswered: boolean;
  }
  
  export interface GetHistoryResponse {
    history: HistoryEntry[];
    totalCount: number;
    availableYears: number[];
    availableMonths: { [year: string]: number[] };
  }  