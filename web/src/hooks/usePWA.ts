// usePWA.ts - Custom hook for handling PWA installation
import { useState, useEffect } from 'react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface UsePWAReturn {
  isPWASupported: boolean;
  isInstalled: boolean;
  canInstall: boolean;
  promptInstall: () => Promise<void>;
}

export default function usePWA(): UsePWAReturn {
  // Track if PWA is supported
  const [isPWASupported, setIsPWASupported] = useState<boolean>(false);
  
  // Track if app is installed
  const [isInstalled, setIsInstalled] = useState<boolean>(false);
  
  // Store the installation prompt event
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  
  // Check if the app can be installed (prompt available)
  const canInstall = Boolean(deferredPrompt);
  
  useEffect(() => {
    // Check if running as installed PWA or within browser
    if (window.matchMedia('(display-mode: standalone)').matches || 
        (window.navigator as any).standalone === true) {
      setIsInstalled(true);
    }
    
    // Listen for beforeinstallprompt event (fires when app can be installed)
    const handler = (e: Event) => {
      // Prevent default browser install prompt
      e.preventDefault();
      // Store the event for later use
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // Mark PWA as supported
      setIsPWASupported(true);
    };
    
    // Listen for appinstalled event
    const installedHandler = () => {
      setIsInstalled(true);
      setDeferredPrompt(null);
    };
    
    window.addEventListener('beforeinstallprompt', handler);
    window.addEventListener('appinstalled', installedHandler);
    
    // Check if PWA is supported through presence of service worker
    setIsPWASupported('serviceWorker' in navigator);
    
    return () => {
      window.removeEventListener('beforeinstallprompt', handler);
      window.removeEventListener('appinstalled', installedHandler);
    };
  }, []);
  
  // Function to prompt user to install the PWA
  const promptInstall = async () => {
    if (!deferredPrompt) {
      console.log('Installation prompt not available');
      return;
    }
    
    // Show the installation prompt
    await deferredPrompt.prompt();
    
    // Wait for user's choice
    const choiceResult = await deferredPrompt.userChoice;
    
    if (choiceResult.outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
    }
    
    // Clear the prompt after use
    setDeferredPrompt(null);
  };
  
  return {
    isPWASupported,
    isInstalled,
    canInstall,
    promptInstall,
  };
}
