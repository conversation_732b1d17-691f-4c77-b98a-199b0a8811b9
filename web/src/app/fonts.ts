import { Outfit, Inter, Crimson_Pro } from 'next/font/google';
import localFont from 'next/font/local';

// Google Fonts
export const outfit = Outfit({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-outfit',
});

export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

export const crimsonPro = Crimson_Pro({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-crimson-pro',
});

// Local custom font for headings

export const superShiny = localFont({
  src: '../../public/fonts/SuperShiny.ttf',
  variable: '--font-super-shiny',
  display: 'swap',
});