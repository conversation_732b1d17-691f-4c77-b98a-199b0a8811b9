import { Outfit, Inter, Crimson_Pro, Funnel_Display} from 'next/font/google';
import localFont from 'next/font/local';

// Google Fonts
export const outfit = Outfit({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-outfit',
});

export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

export const crimsonPro = Crimson_Pro({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-crimson-pro',
});

export const funnelDisplay = Funnel_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-funnel-display',
});

// Local custom font for headings

export const superShiny = localFont({
  src: '../../public/fonts/SuperShiny.ttf',
  variable: '--font-super-shiny',
  display: 'swap',
});

export const heartLove = localFont({
  src: '../../public/fonts/HeartLove.ttf',
  variable: '--font-heart-love',
  display: 'swap',
});

export const cookieMonster = localFont({
  src: '../../public/fonts/Cookiemonster.ttf',
  variable: '--font-cookie-monster',
  display: 'swap',
});