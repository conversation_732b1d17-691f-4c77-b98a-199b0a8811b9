'use client';

import RegisterForm from "@/components/RegisterForm";
import { useAppSelector } from "@/store/hooks";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function RegisterPage() {
  const { user, loading } = useAppSelector((state) => state.auth);
  const router = useRouter();

  // Redirect authenticated users
  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [loading, user, router]);

  return (
      <div className="flex items-center justify-center px-6 py-12 lg:px-8 min-h-[calc(100vh-12rem)]">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Create an account
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Sign up to get started with Novan
            </p>
          </div>
          
          <RegisterForm />
          
          <div className="mt-6 text-center">
            <p className="text-gray-600 dark:text-gray-300">
              Already have an account?{" "}
              <Link href="/login" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
  );
}