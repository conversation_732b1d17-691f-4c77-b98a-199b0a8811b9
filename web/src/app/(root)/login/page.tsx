'use client';

import LoginForm from "@/components/LoginForm";
import { useAppSelector } from "@/store/hooks";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function LoginPage() {
  const { user, loading } = useAppSelector((state) => state.auth);
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

  // Redirect authenticated users
  useEffect(() => {
    if (!loading && user) {
      router.push(callbackUrl);
    }
  }, [loading, user, router, callbackUrl]);

  return (
      <div className="flex items-center justify-center px-6 py-12 lg:px-8 min-h-[calc(100vh-12rem)]">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Welcome back
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Sign in to continue to Novan
            </p>
          </div>
          
          <LoginForm />
          
          <div className="mt-6 text-center">
            <p className="text-gray-600 dark:text-gray-300">
              Don&apos;t have an account?{" "}
              <Link href="/register" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
  );
}