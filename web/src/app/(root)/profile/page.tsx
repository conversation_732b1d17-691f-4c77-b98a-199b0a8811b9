"use client";

import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updatePrivacySettings, updateUserProfile } from "@/utils/api";
import { updateUserProfile as updateUserProfileAction } from "@/store/features/auth/authSlice";
import { useEffect, useState } from "react";

export default function ProfilePage() {
  const { user } = useAppSelector((state) => state.auth);
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState(user?.firstName || "");
  const [lastName, setLastName] = useState(user?.lastName || "");
  const [bio, setBio] = useState(user?.bio || "");
  const [showAnswers, setShowAnswers] = useState(
    user?.preferences?.privacySettings?.showAnswers !== false
  );
  const [showProfile, setShowProfile] = useState(
    user?.preferences?.privacySettings?.showProfile !== false
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isSavingPrivacy, setIsSavingPrivacy] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const dispatch = useAppDispatch();

  const handleSave = async () => {
    setIsSaving(true);
    setSuccessMessage("");

    try {
      const { user: updatedUser } = await updateUserProfile(
        firstName,
        lastName,
        bio
      );
      dispatch(updateUserProfileAction(updatedUser));

      // Show success message
      setSuccessMessage("Profile updated successfully!");
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving profile:", error);
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    const handleSavePrivacySettings = async () => {
      setIsSavingPrivacy(true);
      setSuccessMessage("");

      try {
        const { user: updatedUser } = await updatePrivacySettings(
          showAnswers,
          showProfile
        );
        dispatch(updateUserProfileAction(updatedUser));

        // Show success message
        setSuccessMessage("Privacy settings updated successfully!");
      } catch (error) {
        console.error("Error saving privacy settings:", error);
      } finally {
        setIsSavingPrivacy(false);
      }
    };

    if (
      showAnswers !== user?.preferences?.privacySettings?.showAnswers ||
      showProfile !== user?.preferences?.privacySettings?.showProfile
    ) {
      handleSavePrivacySettings();
    }
  }, [showAnswers, showProfile, user, dispatch]);

  return (
      <div className="space-y-6">
        {/* Basic Profile Information */}
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6">
          <div className="flex justify-between items-start mb-6">
            <h2 className="text-xl font-semibold">Profile Information</h2>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="px-4 py-2 rounded-md text-sm font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800">
              {isEditing ? "Cancel" : "Edit Profile"}
            </button>
          </div>

          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {successMessage}
            </div>
          )}

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Name</p>
                {isEditing ? (
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="First Name"
                    />
                    <input
                      type="text"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Last Name"
                    />
                  </div>
                ) : (
                  <p className="font-medium">
                    {user?.firstName} {user?.lastName}
                  </p>
                )}
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Email
                </p>
                <p className="font-medium">{user?.email}</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                Bio
              </p>
              {isEditing ? (
                <textarea
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Tell us about yourself"
                />
              ) : (
                <p className="font-medium">{bio || "No bio provided"}</p>
              )}
            </div>

            {isEditing && (
              <div className="pt-4 flex justify-end">
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="px-4 py-2 rounded-md text-sm font-medium bg-blue-600 text-white hover:bg-blue-500 disabled:opacity-50">
                  {isSaving ? "Saving..." : "Save Changes"}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Privacy Settings */}
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-6">Privacy Settings</h2>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Show my answers to partner</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  When enabled, your partner can see your answers once both of
                  you have responded
                </p>
              </div>
              <div className="flex items-center">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showAnswers}
                    onChange={() => setShowAnswers(!showAnswers)}
                    className="sr-only peer"
                  />
                  {!isSavingPrivacy ? (
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  ) : (
                    <div className="w-11 h-6 bg-gray-300 rounded-full dark:bg-gray-700 dark:opacity-50 animate-spin"></div>
                  )}
                </label>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Show my profile information</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  When enabled, your profile information is visible to your
                  partner
                </p>
              </div>
              <div className="flex items-center">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showProfile}
                    onChange={() => setShowProfile(!showProfile)}
                    className="sr-only peer"
                  />
                  {!isSavingPrivacy ? (
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  ) : (
                    <div className="w-11 h-6 bg-gray-300 rounded-full dark:bg-gray-700 dark:opacity-50 animate-spin"></div>
                  )}
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Account Settings */}
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-6">Account Settings</h2>

          <div className="space-y-4">
            <button className="px-4 py-2 rounded-md text-sm font-medium border border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
              Change Password
            </button>

            <button className="px-4 py-2 rounded-md text-sm font-medium text-red-700 hover:text-white border border-red-700 hover:bg-red-800 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-500 dark:hover:text-white">
              Delete Account
            </button>
          </div>
        </div>
      </div>
  );
}
