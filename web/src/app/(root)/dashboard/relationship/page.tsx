/* eslint-disable @typescript-eslint/no-explicit-any */
// filepath: /home/<USER>/open-source/novan/web/src/app/dashboard/relationship/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppSelector } from "@/store/hooks";
import {
  getPendingRelationships,
  sendRelationshipRequest,
  acceptRelationshipRequest,
  declineRelationshipRequest,
  cancelRelationshipRequest,
} from "@/utils/api";
import {
  PendingRelationshipsResponse,
  Relationship,
} from "@/types/api/relationship/relationship.type";

export default function RelationshipPage() {
  const router = useRouter();
  const { user } = useAppSelector((state) => state.auth);
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [pendingRelationships, setPendingRelationships] = useState<
    PendingRelationshipsResponse | Relationship | null
  >(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch pending relationships on component mount
  useEffect(() => {
    // Only fetch if user is authenticated
    if (!user) return;

    const fetchPendingRelationships = async () => {
      try {
        setLoading(true);
        const data = await getPendingRelationships();
        setPendingRelationships(data);
      } catch (err) {
        setError("Failed to load relationship requests");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchPendingRelationships();
  }, [user]);

  const handleSendRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");
    setSuccess("");

    try {
      const result = await sendRelationshipRequest(
        email,
      );
      if (result.message && !result.relationship) {
        setError(result.message);
      } else {
        setSuccess("Relationship request sent successfully");
        setEmail("");
        // Refresh pending relationships
        const updatedPendingRelationships = await getPendingRelationships();
        setPendingRelationships(updatedPendingRelationships);
      }
    } catch (err: any) {
      setError(err.message || "Failed to send relationship request");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAcceptRequest = async (relationshipId: string) => {
    try {
      setLoading(true);
      await acceptRelationshipRequest(relationshipId);
      setSuccess("Relationship request accepted");

      // Refresh the page after accepting relationship
      // router.refresh();

      // Optionally redirect to dashboard or profile
      router.push("/dashboard");
    } catch (err: any) {
      setError(err.message || "Failed to accept relationship request");
    } finally {
      setLoading(false);
    }
  };

  const handleDeclineRequest = async (relationshipId: string) => {
    try {
      setLoading(true);
      await declineRelationshipRequest(relationshipId);

      // Refresh pending relationships
      const updatedPendingRelationships = await getPendingRelationships();
      setPendingRelationships(updatedPendingRelationships);

      setSuccess("Relationship request declined");
    } catch (err: any) {
      setError(err.message || "Failed to decline relationship request");
    } finally {
      setLoading(false);
    }
  };

  const handleCancelRequest = async (relationshipId: string) => {
    try {
      setLoading(true);
      await cancelRelationshipRequest(relationshipId);

      // Refresh pending relationships
      const updatedPendingRelationships = await getPendingRelationships();
      setPendingRelationships(updatedPendingRelationships);

      setSuccess("Relationship request canceled");
    } catch (err: any) {
      setError(err.message || "Failed to cancel relationship request");
    } finally {
      setLoading(false);
    }
  };

  // If user is not loaded yet, show a loading indicator
  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        Loading...
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 bg-gray-100 dark:bg-gray-900">
      <h1 className="text-3xl font-bold mb-8 text-center">
        Relationship Management
      </h1>

      {pendingRelationships && "userOne" in pendingRelationships ? (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
          <p className="text-green-600">
            You are already connected with{" "}
            {typeof pendingRelationships.userOne === "object" &&
            "firstName" in pendingRelationships.userOne
              ? pendingRelationships.userOne.firstName
              : pendingRelationships.userOne}
            .
          </p>
        </div>
      ) : (
        <>
          {/* Send Relationship Request Form */}
          <div className="bg-white p-6 rounded-lg shadow-md mb-8 dark:bg-gray-800">
            <h2 className="text-xl font-semibold mb-4">
              Send a Relationship Request
            </h2>
            <form onSubmit={handleSendRequest} className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium dark:text-white text-gray-700 mb-1">
                  Partner&apos;s Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your partner's email"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition duration-300 disabled:opacity-50">
                {isSubmitting ? "Sending..." : "Send Request"}
              </button>

              {error && (
                <div className="p-3 bg-red-100 text-red-700 rounded-md">
                  {error}
                </div>
              )}

              {success && (
                <div className="p-3 bg-green-100 text-green-700 rounded-md">
                  {success}
                </div>
              )}
            </form>
          </div>

          {/* Pending Requests Sections */}
          <div className="space-y-8">
            {loading && (
              <div className="text-center">
                Loading relationship requests...
              </div>
            )}

            {/* Incoming Requests */}
            {pendingRelationships &&
              pendingRelationships.incoming.length > 0 && (
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                  <h2 className="text-xl font-semibold mb-4">
                    Incoming Relationship Requests
                  </h2>
                  <div className="space-y-4">
                    {pendingRelationships.incoming.map((relationship) => (
                      <div
                        key={relationship._id}
                        className="border p-4 rounded-md">
                        <p className="font-medium">
                          <span className="font-semibold">From:</span>{" "}
                          {`${
                            relationship.metadata.initiator !==
                            relationship.userOne._id
                              ? relationship.userTwo.firstName
                              : relationship.userOne.firstName
                          }`}
                        </p>
                        <p className="text-gray-600 text-xs dark:text-white mb-4">
                          {`${
                            relationship.metadata.initiator !==
                            relationship.userOne._id
                              ? relationship.userTwo.email
                              : relationship.userOne.email
                          }`}
                        </p>
                        <div className="flex space-x-3">
                          <button
                            onClick={() =>
                              handleAcceptRequest(relationship._id)
                            }
                            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-300"
                            disabled={loading}>
                            Accept
                          </button>
                          <button
                            onClick={() =>
                              handleDeclineRequest(relationship._id)
                            }
                            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition duration-300"
                            disabled={loading}>
                            Decline
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* Outgoing Requests */}
            {pendingRelationships &&
              pendingRelationships.outgoing.length > 0 && (
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                  <h2 className="text-xl font-semibold mb-4">
                    Outgoing Relationship Requests
                  </h2>
                  <div className="space-y-4">
                    {pendingRelationships.outgoing.map((relationship) => (
                      <div
                        key={relationship._id}
                        className="border p-4 rounded-md">
                        <p className="font-medium">
                          <span className="font-semibold">To:</span>{" "}
                          {`${
                            relationship.metadata.initiator ===
                            relationship.userOne._id
                              ? relationship.userTwo.firstName
                              : relationship.userOne.firstName
                          }`}
                        </p>
                        <p className="text-gray-600 dark:text-white mb-4">
                          {`${
                            relationship.metadata.initiator ===
                            relationship.userOne._id
                              ? relationship.userTwo.email
                              : relationship.userOne.email
                          }`}
                        </p>
                        <p className="text-yellow-600 mb-2">Status: Pending</p>
                        <button
                          onClick={() => handleCancelRequest(relationship._id)}
                          className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition duration-300"
                          disabled={loading}>
                          Cancel Request
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* No Pending Requests */}
            {pendingRelationships &&
              pendingRelationships.incoming.length === 0 &&
              pendingRelationships.outgoing.length === 0 && (
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
                  <p className="text-gray-600">
                    You have no pending relationship requests.
                  </p>
                </div>
              )}
          </div>
        </>
      )}
    </div>
  );
}
