'use client';
import { useEffect, useState } from 'react';
import TimezoneSettings from '@/components/relationship/TimezoneSettings';
import { getUserProfile } from '@/utils/api';

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeRelationship, setActiveRelationship] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [timezonePrefs, setTimezonePrefs] = useState({
    timezone: '',
    preferredTime: ''
  });
  
  // Fix: Use a ref to prevent the effect from running multiple times
  const [dataFetched, setDataFetched] = useState(false);
  
  useEffect(() => {
    // Only fetch data once
    if (dataFetched) return;
    
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // Get user profile to check for active relationship
        const { user, relationship } = await getUserProfile();
        
        // Check if the user has an active relationship
        if (user.partner && user.partner.status === 'active') {
          setActiveRelationship(user.partner);
          
          if (relationship && relationship.metadata) {
            setTimezonePrefs({
              timezone: relationship.metadata.timezone || '',
              preferredTime: relationship.metadata.preferredQuestionTime || ''
            });
          }
        }
        
        // Set the flag to prevent refetching
        setDataFetched(true);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load user data';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [dataFetched]); // Only depend on dataFetched flag
  
  const handleSuccess = () => {
    // Show success message and optionally refresh data
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <p className="text-gray-400 dark:text-gray-300">Loading settings...</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
      
      {error && (
        <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      {!activeRelationship ? (
        <div className="bg-amber-100 border border-amber-400 text-amber-800 rounded-lg p-4">
          <p>You need to be in an active relationship to set timezone preferences.</p>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <div className="bg-gray-700 rounded-lg p-4 mb-6">
              <h2 className="text-lg font-semibold text-white">Question Delivery Preferences</h2>
              <p className="text-gray-300 text-sm">
                Configure when your daily relationship questions should arrive based on your timezone.
                Questions will be delivered at your selected time each day.
              </p>
            </div>
            
            <TimezoneSettings 
              initialTimezone={timezonePrefs.timezone}
              initialTime={timezonePrefs.preferredTime}
              onSuccess={handleSuccess} 
            />
          </div>
        </div>
      )}
    </div>
  );
}