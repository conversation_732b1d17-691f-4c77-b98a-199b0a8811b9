"use client";
import { getAnswerHistory } from "@/utils/api";
import { setQuestionHistory } from "@/store/features/questions/questionsSlice";
import {
  setHeartsCount,
  setIsLetterOpen,
} from "@/store/features/hearts/heartsSlice";
import { useAppDispatch } from "@/store/hooks";
import LetterModal from "@/components/LetterModal/LetterModal";
import { useAppSelector } from "@/store/hooks";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { LetterModalContainer } from "@/components/LetterModal/LetterModalContainer";
import ClusterSelector from "@/components/ClusterSelector";

const HeartsClusterScene = dynamic(
  () => import("@/components/HeartsClusterScene"),
  { ssr: false }
);

const Page: React.FC = () => {
  const dispatch = useAppDispatch();
  const count = useAppSelector((state) => state.hearts?.count);
  const isLetterOpen = useAppSelector((state) => state.hearts?.isLetterOpen);
  const [month, setMonth] = useState<number>(new Date().getMonth() + 1);
  const [year, setYear] = useState<number>(new Date().getFullYear());

  useEffect(() => {
    const fetchQuestionsHistory = async () => {
      try {
        const response = await getAnswerHistory(month, year);
        dispatch(setQuestionHistory(response.history));
        dispatch(setHeartsCount(response.totalCount));
      } catch (err) {
        console.error("Error fetching question history:", err);
      }
    };

    fetchQuestionsHistory();
  }, [dispatch, month, year]);

  const handleLetterClose = () => {
    dispatch(setIsLetterOpen(false));
  };

  return (
    <div
      className={`border-2 ${
        isLetterOpen ? "border-red-500" : "border-transparent"
      } rounded-lg w-full h-full`}>
      <ClusterSelector
        month={month}
        year={year}
        setMonth={setMonth}
        setYear={setYear}
      />
      {count !== undefined && count !== 0 ? (
        <HeartsClusterScene count={count} />
      ) : (
        <div className="w-full h-full flex justify-center items-center">
          <p className="text-2xl font-bold">
            No questions answered in this month and year
          </p>
        </div>
      )}
      <LetterModalContainer isOpen={isLetterOpen} onClose={handleLetterClose}>
        <LetterModal />
      </LetterModalContainer>
    </div>
  );
};
export default Page;
