"use client";
import { getAnswerHistory } from "@/utils/api";
import { setQuestionHistory } from "@/store/features/questions/questionsSlice";
import {
  setHeartsCount,
  setIsLetterOpen,
} from "@/store/features/hearts/heartsSlice";
import { useAppDispatch } from "@/store/hooks";
import LetterModal from "@/components/LetterModal/LetterModal";
import { useAppSelector } from "@/store/hooks";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { LetterModalContainer } from "@/components/LetterModal/LetterModalContainer";
import SculptedTimeline from "@/components/3D/SculptedTimeline";
import { EnvelopeOpenIcon } from "@heroicons/react/24/outline";
import { selectEffectiveTheme } from "@/store/features/theme/themeSlice";

const HeartsClusterScene = dynamic(
  () => import("@/components/HeartsClusterScene"),
  { ssr: false }
);

const Page: React.FC = () => {
  const dispatch = useAppDispatch();
  const count = useAppSelector((state) => state.hearts?.count);
  const isLetterOpen = useAppSelector((state) => state.hearts?.isLetterOpen);
  const theme = useAppSelector(selectEffectiveTheme);
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const [month, setMonth] = useState<number>(currentMonth);
  const [year, setYear] = useState<number>(currentYear);
  const { relationship } = useAppSelector((state) => state.auth);
  const initYear = relationship?.activatedAt
    ? new Date(relationship.activatedAt).getFullYear()
    : new Date().getFullYear();
  const initMonth = relationship?.activatedAt
    ? new Date(relationship.activatedAt).getMonth() + 1
    : new Date().getMonth() + 1;
  const timelineData: { year: number; months: number[] }[] = [];
  // Initialize timeline data with all months from relationship start to current date
  let currentYearIdx = 0;
  for (let y = initYear; y <= currentYear; y++) {
    timelineData.push({ year: y, months: [] });

    // Determine start month (use initMonth for first year, otherwise start from January)
    const startMonth = y === initYear ? initMonth : 1;

    // Determine end month (use currentMonth for current year, otherwise go through December)
    const endMonth = y === currentYear ? currentMonth : 12;

    for (let m = startMonth; m <= endMonth; m++) {
      timelineData[currentYearIdx].months.push(m);
    }

    currentYearIdx++;
  }

  useEffect(() => {
    const fetchQuestionsHistory = async () => {
      try {
        const response = await getAnswerHistory(month, year);
        dispatch(setQuestionHistory(response.history));
        dispatch(setHeartsCount(response.totalCount));
      } catch (err) {
        console.error("Error fetching question history:", err);
      }
    };

    fetchQuestionsHistory();
  }, [dispatch, month, year]);

  const handleLetterClose = () => {
    dispatch(setIsLetterOpen(false));
  };

  return (
    <div
      className={`border-2 ${
        isLetterOpen ? "border-red-500" : theme === "dark" ? "border-black" : "border-background"
      } w-full h-full`}>
      <SculptedTimeline
        data={timelineData}
        selectedYear={year}
        selectedMonth={month}
        onDateSelect={(year, month) => {
          setYear(year);
          setMonth(month);
        }}
      />
      {count !== undefined && count !== 0 ? (
        <HeartsClusterScene count={count} />
      ) : (
        <div className="w-full h-full flex justify-center items-center">
          {/* A card with a glass type feel background look with empty envelope showing sad vibe */}
          <div className="max-w-md w-full p-8 rounded-xl backdrop-filter backdrop-blur-lg bg-white/30 dark:bg-gray-400/30 border border-white/20 dark:border-gray-700/20 shadow-xl">
            <div className="flex flex-col items-center justify-center space-y-4 text-gray-600 dark:text-gray-300">
              <EnvelopeOpenIcon className="h-24 w-24 text-gray-400 animate-pulse" />
              <h3 className="text-xl font-semibold text-center">No Memories Found</h3>
              <p className="text-center text-sm opacity-75">
                There are no cherished moments saved for this time period. Create new memories together.
              </p>
            </div>
          </div>
        </div>
      )}
      <LetterModalContainer isOpen={isLetterOpen} onClose={handleLetterClose}>
        <LetterModal />
      </LetterModalContainer>
    </div>
  );
};
export default Page;
