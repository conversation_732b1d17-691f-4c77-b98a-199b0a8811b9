'use client';

import {  useEffect, useRef } from 'react';
import PageTransition from '@/components/shared/PageTransition';
import lottie from 'lottie-web';
import HeroSection from '@/components/Landing/HeroSection';
import FeaturesSection from '@/components/Landing/FeaturesSection';
import HowItWorksSection from '@/components/Landing/HowItWorksSection';

export default function Home() {
  // Refs for animations
  const lottieRef = useRef<HTMLDivElement>(null);

  // Lottie animation for "how it works" section
  useEffect(() => {
    if (lottieRef.current) {
      const anim = lottie.loadAnimation({
        container: lottieRef.current,
        renderer: 'svg',
        loop: true,
        autoplay: true,
        path: '/animations/connections-animation.json' // You would need to add a Lottie JSON file to your public directory
      });

      return () => anim.destroy();
    }
  }, [lottieRef]);

  return (
    <PageTransition>
      <div className="relative min-h-screen overflow-hidden">
        <HeroSection />
        <FeaturesSection />
        <HowItWorksSection />
        {/* Footer */}
        <footer className="py-12 text-center text-gray-500 dark:text-gray-400 z-10">
          <p>&copy; {new Date().getFullYear()} Novan. All rights reserved.</p>
          <p className="mt-2 text-sm">
            Novan is designed for couples seeking to enhance their connection and is not a substitute for professional counseling.
          </p>
        </footer>
      </div>
    </PageTransition>
  );
}
