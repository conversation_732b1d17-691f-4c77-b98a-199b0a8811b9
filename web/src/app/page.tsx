'use client';

import {  useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { useInView } from 'framer-motion'; // Using framer's inView for simplicity
import PageTransition from '@/components/shared/PageTransition';
import { ChatBubbleLeftRightIcon, HeartIcon, CalendarDaysIcon, UserGroupIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import lottie from 'lottie-web';
import HeroSection from '@/components/Landing/HeroSection';
import FeaturesSection from '@/components/Landing/FeaturesSection';

export default function Home() {
  // Refs for animations
  const lottieRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  // Reference for Question of the Day section
  const questionSectionRef = useRef(null);
  const isQuestionInView = useInView(questionSectionRef, { once: false, amount: 0.5 });

  // const features = [
  //   {
  //     id: 1,
  //     title: 'Daily Questions',
  //     description: 'New thought-provoking question delivered to both partners every day',
  //     icon: <CalendarDaysIcon className="w-8 h-8" />,
  //     color: 'from-purple-500 to-indigo-600'
  //   },
  //   {
  //     id: 2,
  //     title: 'Private Sharing',
  //     description: 'Answers remain hidden until both partners respond, ensuring authentic reflection',
  //     icon: <LockClosedIcon className="w-8 h-8" />,
  //     color: 'from-rose-400 to-pink-600'
  //   },
  //   {
  //     id: 3,
  //     title: 'Connection History',
  //     description: 'Review past questions and answers to see how your relationship evolves',
  //     icon: <ChatBubbleLeftRightIcon className="w-8 h-8" />,
  //     color: 'from-teal-400 to-cyan-600'
  //   },
  //   {
  //     id: 4,
  //     title: 'Partner Sync',
  //     description: 'Seamlessly connect with your partner across all devices',
  //     icon: <UserGroupIcon className="w-8 h-8" />,
  //     color: 'from-amber-400 to-orange-600'
  //   }
  // ];

  // Testimonials for the testimonials section
  const testimonials = [
    {
      id: 1,
      name: "Jordan & Taylor",
      text: "Novan helped us discover things about each other we never knew, even after 5 years together!",
      highlight: "discover things about each other"
    },
    {
      id: 2,
      name: "Alex & Morgan",
      text: "The daily questions have become our favorite ritual. We look forward to them every evening.",
      highlight: "favorite ritual"
    },
    {
      id: 3,
      name: "Riley & Casey",
      text: "We were stuck in a communication rut until Novan. Now we have meaningful conversations daily.",
      highlight: "meaningful conversations daily"
    }
  ];

  // Lottie animation for "how it works" section
  useEffect(() => {
    if (lottieRef.current) {
      const anim = lottie.loadAnimation({
        container: lottieRef.current,
        renderer: 'svg',
        loop: true,
        autoplay: true,
        path: '/animations/connections-animation.json' // You would need to add a Lottie JSON file to your public directory
      });

      return () => anim.destroy();
    }
  }, [lottieRef]);


  // Today's date for the "Question of the Day" section
  const today = format(new Date(), 'MMMM d, yyyy');

  return (
    <PageTransition>
      <div className="relative min-h-screen overflow-hidden">
        {/* Hero Section Component */}
        <HeroSection />
        <FeaturesSection />

        {/* Question of the Day Section */}
        <motion.section
          ref={questionSectionRef}
          className="relative py-16 lg:py-24 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.7 }}
        >
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/30 dark:to-indigo-900/30 shadow-2xl p-8 md:p-12">
              <div className="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full opacity-20 blur-3xl"></div>
              <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full opacity-20 blur-3xl"></div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <motion.div
                  initial={{ x: -50, opacity: 0 }}
                  animate={isQuestionInView ? { x: 0, opacity: 1 } : { x: -50, opacity: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="text-sm font-medium text-indigo-600 dark:text-indigo-400 mb-2">{today}</div>
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Question of the Day</h2>
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-6">
                    <p className="text-xl md:text-2xl text-gray-800 dark:text-gray-200 italic">
                      &quot;What&apos;s one dream you&apos;ve never told me about?&quot;
                    </p>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Each day brings a new opportunity to learn something meaningful about your partner.
                    The questions are designed to gradually build intimacy and understanding.
                  </p>
                  <motion.button
                    className="rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-3 text-white font-medium shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    See More Sample Questions
                  </motion.button>
                </motion.div>

                <motion.div
                  className="relative"
                  initial={{ x: 50, opacity: 0 }}
                  animate={isQuestionInView ? { x: 0, opacity: 1 } : { x: 50, opacity: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <div className="relative rounded-xl overflow-hidden aspect-[4/3] bg-gray-100 dark:bg-gray-700">
                    {/* This would be replaced by actual image */}
                    <div ref={lottieRef} className="w-full h-full"></div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Testimonials Section */}
        <motion.section
          ref={testimonialsRef}
          className="relative py-16 lg:py-24 bg-gray-50 dark:bg-gray-900 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.7 }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Stories from Our Community
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                See how Novan is transforming relationships around the world
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.id}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 flex flex-col"
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="mb-4 text-2xl text-purple-500 dark:text-purple-400">
                    <HeartIcon className="w-8 h-8 inline-block" />
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 flex-grow mb-4">
                    {testimonial.text.split(testimonial.highlight).map((part, i, arr) => (
                      <span key={i}>
                        {part}
                        {i < arr.length - 1 && (
                          <span className="font-bold text-indigo-600 dark:text-indigo-400">
                            {testimonial.highlight}
                          </span>
                        )}
                      </span>
                    ))}
                  </p>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {testimonial.name}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section>

        {/* How It Works Section with Animation */}
        <motion.section
          className="relative py-16 lg:py-24 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.7 }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                How Novan Works
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Simple, thoughtful, and designed for real connection
              </p>
            </div>

            <div className="relative">
              {/* Connection line */}
              <div className="absolute left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-500 via-indigo-500 to-purple-500 hidden md:block"></div>

              <div className="space-y-16 md:space-y-24 relative">
                <motion.div
                  className="relative grid grid-cols-1 md:grid-cols-2 gap-8 items-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="md:text-right order-2 md:order-1">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                      1. Daily Question Delivery
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Each day at your preferred time, you and your partner receive the same thought-provoking question. Questions range from lighthearted to deep, creating opportunities for meaningful exchange.
                    </p>
                  </div>
                  <div className="order-1 md:order-2 flex justify-center md:justify-start">
                    <div className="relative">
                      <div className="absolute -inset-4 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full opacity-20 blur-lg"></div>
                      <div className="relative flex items-center justify-center w-24 h-24 rounded-full bg-white dark:bg-gray-800 shadow-xl border-4 border-purple-500 dark:border-purple-400">
                        <CalendarDaysIcon className="w-12 h-12 text-purple-500 dark:text-purple-400" />
                      </div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="relative grid grid-cols-1 md:grid-cols-2 gap-8 items-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <div className="md:text-left">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                      2. Private Reflection
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Both partners answer the question independently. Answers are kept private until both are complete, encouraging honest and authentic responses.
                    </p>
                  </div>
                  <div className="flex justify-center md:justify-end">
                    <div className="relative">
                      <div className="absolute -inset-4 bg-gradient-to-r from-rose-500 to-pink-600 rounded-full opacity-20 blur-lg"></div>
                      <div className="relative flex items-center justify-center w-24 h-24 rounded-full bg-white dark:bg-gray-800 shadow-xl border-4 border-rose-500 dark:border-rose-400">
                        <LockClosedIcon className="w-12 h-12 text-rose-500 dark:text-rose-400" />
                      </div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="relative grid grid-cols-1 md:grid-cols-2 gap-8 items-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <div className="md:text-right order-2 md:order-1">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                      3. Reveal and Discuss
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Once both answers are submitted, reveal your responses to each other. Discuss your thoughts, feelings, and any new insights gained.
                    </p>
                  </div>
                  <div className="order-1 md:order-2 flex justify-center md:justify-start">
                    <div className="relative">
                      <div className="absolute -inset-4 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-full opacity-20 blur-lg"></div>
                      <div className="relative flex items-center justify-center w-24 h-24 rounded-full bg-white dark:bg-gray-800 shadow-xl border-4 border-teal-500 dark:border-teal-400">
                        <ChatBubbleLeftRightIcon className="w-12 h-12 text-teal-500 dark:text-teal-400" />
                      </div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="relative grid grid-cols-1 md:grid-cols-2 gap-8 items-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                >
                  <div className="md:text-left">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                      4. Build Your Connection History
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Over time, create a rich history of your shared thoughts and feelings. Revisit past questions and answers to reflect on your growth as a couple.
                    </p>
                  </div>
                  <div className="flex justify-center md:justify-end">
                    <div className="relative">
                      <div className="absolute -inset-4 bg-gradient-to-r from-amber-500 to-orange-600 rounded-full opacity-20 blur-lg"></div>
                      <div className="relative flex items-center justify-center w-24 h-24 rounded-full bg-white dark:bg-gray-800 shadow-xl border-4 border-amber-500 dark:border-amber-400">
                        <UserGroupIcon className="w-12 h-12 text-amber-500 dark:text-amber-400" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Footer */}
        <footer className="py-12 text-center text-gray-500 dark:text-gray-400 z-10">
          <p>&copy; {new Date().getFullYear()} Novan. All rights reserved.</p>
          <p className="mt-2 text-sm">
            Novan is designed for couples seeking to enhance their connection and is not a substitute for professional counseling.
          </p>
        </footer>
      </div>
    </PageTransition>
  );
}
