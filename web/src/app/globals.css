@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

:root {
  /* Primary Colors */
  --deep-indigo: #5E60CE;
  --soft-lavender: #7400B8;
  
  /* Secondary Colors */
  --sunset-coral: #FF6B6B;
  --mint-green: #64DFDF;

  /* Light Mode */
  --background: #d0dbf1;
  --foreground: #1F2937;
  --foreground-secondary: #4B5563;
  --card-bg: #cae9fd;
  --card-border: #a9c4fc;
  --input-bg: #FFFFFF;
  --input-border: #D1D5DB;
  --ring: var(--deep-indigo);
  --primary: var(--deep-indigo);
  --primary-foreground: #FFFFFF;
  --secondary: var(--soft-lavender);
  --accent: var(--sunset-coral);
  --accent-secondary: var(--mint-green);
  
  /* How It Works Section */
  --how-it-works-bg-overlay: rgba(255, 255, 255, 0.2);
  --how-it-works-card-bg: rgba(255, 255, 255, 0.05);
  --how-it-works-card-border: rgba(255, 255, 255, 0.1);
  --how-it-works-text-primary: oklch(49.6% 0.265 301.924);
  --how-it-works-text: #1F2937;
  --how-it-works-text-secondary: #4B5563;
  --how-it-works-icon-container: rgba(255, 255, 255, 0.8);
  --how-it-works-icon-border-opacity: 1;
}

.dark {
  /* Dark Mode */
  --background: #111827;
  --foreground: #F3F4F6;
  --foreground-secondary: #D1D5DB;
  --card-bg: #1F2937;
  --card-border: #374151;
  --input-bg: #1F2937;
  --input-border: #374151;
  --ring: var(--deep-indigo);
  --primary: var(--deep-indigo);
  --primary-foreground: #FFFFFF;
  --secondary: var(--soft-lavender);
  --accent: var(--sunset-coral);
  --accent-secondary: var(--mint-green);
  
  /* How It Works Section - Dark Mode */
  --how-it-works-bg-overlay: rgba(0, 0, 0, 0.3);
  --how-it-works-card-bg: rgba(31, 41, 55, 0.1);
  --how-it-works-card-border: rgba(55, 65, 81, 0.3);
  --how-it-works-text-primary: oklch(82.7% 0.119 306.383);
  --how-it-works-text: #F3F4F6;
  --how-it-works-text-secondary: #D1D5DB;
  --how-it-works-icon-container: rgba(31, 41, 55, 0.8);
  --how-it-works-icon-border-opacity: 0.7;
}

@theme {
  /* Theme Variables */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-foreground-secondary: var(--foreground-secondary);
  --color-card: var(--card-bg);
  --color-card-border: var(--card-border);
  --color-input: var(--input-bg);
  --color-input-border: var(--input-border);
  --color-ring: var(--ring);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-accent-secondary: var(--accent-secondary);

  /* Typography */
  --font-heading: var(--font-outfit);
  --font-body: var(--font-funnel-display);
  --font-accent: var(--font-crimson-pro);
  --font-main-heading: var(--font-heart-love);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-body), sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading), sans-serif;
}

.accent-text {
  font-family: var(--font-accent), serif;
}

/* Custom style for main headings using your custom font */
div.main-heading {
  font-family: var(--font-main-heading), sans-serif;
  /* Additional styling for main headings */
}

.header-subtitle {
  font-family: var(--font-funnel-display), sans-serif;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.header-description {
  font-family: var(--font-body), sans-serif;
  font-weight: 400;
  letter-spacing: 0.01em;
}