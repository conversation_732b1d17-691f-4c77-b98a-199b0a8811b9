@import "tailwindcss";

:root {
  /* Primary Colors */
  --deep-indigo: #5E60CE;
  --soft-lavender: #7400B8;
  
  /* Secondary Colors */
  --sunset-coral: #FF6B6B;
  --mint-green: #64DFDF;

  /* Light Mode */
  --background: #d0dbf1;
  --foreground: #1F2937;
  --foreground-secondary: #4B5563;
  --card-bg: #FFFFFF;
  --card-border: #E5E7EB;
  --input-bg: #FFFFFF;
  --input-border: #D1D5DB;
  --ring: var(--deep-indigo);
  --primary: var(--deep-indigo);
  --primary-foreground: #FFFFFF;
  --secondary: var(--soft-lavender);
  --accent: var(--sunset-coral);
  --accent-secondary: var(--mint-green);
}

.dark {
  /* Dark Mode */
  --background: #111827;
  --foreground: #F3F4F6;
  --foreground-secondary: #D1D5DB;
  --card-bg: #1F2937;
  --card-border: #374151;
  --input-bg: #1F2937;
  --input-border: #374151;
  --ring: var(--deep-indigo);
  --primary: var(--deep-indigo);
  --primary-foreground: #FFFFFF;
  --secondary: var(--soft-lavender);
  --accent: var(--sunset-coral);
  --accent-secondary: var(--mint-green);
}

@theme {
  /* Theme Variables */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-foreground-secondary: var(--foreground-secondary);
  --color-card: var(--card-bg);
  --color-card-border: var(--card-border);
  --color-input: var(--input-bg);
  --color-input-border: var(--input-border);
  --color-ring: var(--ring);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-accent-secondary: var(--accent-secondary);

  /* Typography */
  --font-heading: var(--font-outfit);
  --font-body: var(--font-inter);
  --font-accent: var(--font-crimson-pro);
  --font-main-heading: var(--font-super-shiny);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-body), sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading), sans-serif;
}

.accent-text {
  font-family: var(--font-accent), serif;
}

/* Custom style for main headings using your custom font */
h1.main-heading {
  font-family: var(--font-main-heading), sans-serif;
  /* Additional styling for main headings */
  font-weight: 700;
  letter-spacing: 0.01em;
  /* -webkit-text-stroke: 0.015em var(--color-primary); */
  -webkit-text-fill-color: var(--color-primary);
}

.header-subtitle {
  font-family: var(--font-super-shiny), sans-serif;
  font-weight: 500;
  letter-spacing: 0.01em;
}