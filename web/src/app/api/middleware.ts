import { NextRequest, NextResponse } from 'next/server';

const API_URL = process.env.BACKEND_API_URL;

/**
 * Utility function to fetch data from the backend server
 * This centralizes error handling and request configuration
 */
export async function fetchFromServer(
  path: string,
  request: NextRequest,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body?: never
) {
  // Get auth token from headers if it exists
  const authHeader = request.headers.get('Authorization');
  
  // Build query string for GET requests
  const searchParams = method === 'GET' ? request.nextUrl.searchParams.toString() : '';
  const queryString = searchParams ? `?${searchParams}` : '';
  
  try {
    const response = await fetch(`${API_URL}/${path}${queryString}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader ? { 'Authorization': authHeader } : {})
      },
      ...(body ? { body: JSON.stringify(body) } : {}),
    });
    
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error(`Error proxying ${method} request to /${path}:`, error);
    return NextResponse.json(
      { error: 'Failed to reach the backend service' },
      { status: 500 }
    );
  }
}