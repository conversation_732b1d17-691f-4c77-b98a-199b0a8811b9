import './globals.css';
import type { Metadata } from 'next';
import ReduxProvider from '@/components/ReduxProvider';
import ThemeProvider from '@/components/shared/ThemeProvider';
import { outfit, inter, crimsonPro, superShiny } from './fonts';

export const metadata: Metadata = {
  title: 'Novan - Daily Questions for Partners',
  description: 'Connect with your partner through daily questions',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${outfit.variable} ${inter.variable} ${crimsonPro.variable} ${superShiny.variable}`}>
      <body>
        <ReduxProvider>
          <ThemeProvider>
            <div className="min-h-screen">
              {children}
            </div>
          </ThemeProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
