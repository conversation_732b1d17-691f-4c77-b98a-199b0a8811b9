import "./globals.css";
import type { Metadata, Viewport } from "next";
import ReduxProvider from "@/components/ReduxProvider";
import ThemeProvider from "@/components/shared/ThemeProvider";
import {
  outfit,
  inter,
  crimsonPro,
  superShiny,
  heartLove,
  cookieMonster,
} from "./fonts";
import ThemeToggle from "@/components/shared/ThemeToggle";
import dynamic from "next/dynamic";

// Dynamically import PWA components to avoid SSR issues
const PWAWrapper = dynamic(
  () => import('@/components/shared/PWAWrapper'),
  { ssr: false }
);

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#3b82f6'
};

export const metadata: Metadata = {
  title: "Novan - Daily Questions for Partners",
  description: "Connect with your partner through daily questions",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Novan",
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "application-name": "Novan",
    "apple-mobile-web-app-title": "Novan"
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${outfit.variable} ${inter.variable} ${crimsonPro.variable} ${superShiny.variable} ${heartLove.variable} ${cookieMonster.variable}`}>
      <head>
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <script src="/sw-register.js" defer></script>
      </head>
      <body>
        <ReduxProvider>
          <ThemeProvider>
            <div className="min-h-screen">
              {/* Theme Toggle */}
              <div className="fixed -top-2 right-3 z-50 transition-all duration-300 ease-in-out">
                <ThemeToggle
                  variant="bulb"
                  className="transform hover:scale-105 transition-all duration-300"
                />
              </div>
              {children}
              
              {/* PWA Installation Prompt */}
              {/* Only render in client-side production environment */}
              <PWAWrapper />
            </div>
          </ThemeProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
