import "./globals.css";
import type { Metadata } from "next";
import ReduxProvider from "@/components/ReduxProvider";
import ThemeProvider from "@/components/shared/ThemeProvider";
import {
  outfit,
  inter,
  crimsonPro,
  superShiny,
  heartLove,
  cookieMonster,
} from "./fonts";
import ThemeToggle from "@/components/shared/ThemeToggle";

export const metadata: Metadata = {
  title: "Novan - Daily Questions for Partners",
  description: "Connect with your partner through daily questions",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${outfit.variable} ${inter.variable} ${crimsonPro.variable} ${superShiny.variable} ${heartLove.variable} ${cookieMonster.variable}`}>
      <body>
        <ReduxProvider>
          <ThemeProvider>
            <div className="min-h-screen">
              {/* Theme Toggle */}
              <div className="fixed -top-2 right-3 z-50 transition-all duration-300 ease-in-out">
                <ThemeToggle
                  variant="bulb"
                  className="transform hover:scale-105 transition-all duration-300"
                />
              </div>
              {children}
            </div>
          </ThemeProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
