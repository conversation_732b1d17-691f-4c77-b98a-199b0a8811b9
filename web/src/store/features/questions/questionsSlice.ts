"use client";

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { DailyQuestion } from "@/types/api/questions/get-daily.type";
import { HistoryEntry } from "@/types/api/questions/get-history.type";

// Define interface for the questions state
interface QuestionsState {
  dailyQuestion: DailyQuestion | null;
  questionHistory: HistoryEntry[];
}

// Define the initial state
const initialState: QuestionsState = {
  dailyQuestion: null,
  questionHistory: [],
};

// Create the questions slice
const questionsSlice = createSlice({
  name: "questions",
  initialState,
  reducers: {
    setDailyQuestion: (state, action: PayloadAction<DailyQuestion | null>) => {
      state.dailyQuestion = action.payload;
    },
    setQuestionHistory: (state, action: PayloadAction<HistoryEntry[]>) => {
      state.questionHistory = [...action.payload];
    },
  },
});

// Export actions and reducer
export const { setDailyQuestion, setQuestionHistory } = questionsSlice.actions;
export default questionsSlice.reducer;
