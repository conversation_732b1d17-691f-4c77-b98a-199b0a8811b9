"use client";

import { Relationship } from "@/types/api";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Define interface for the authentication state
interface AuthState {
  user: {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
    name?: string; // Computed from firstName + lastName
    isActive: boolean;
    bio?: string;
    partnerId: string | null;
    partner?: {
      id: string;
      status: "active" | "paused" | "ended";
    };
    createdAt: string;
    updatedAt: string;
    preferences?: {
      emailNotifications: boolean;
      privacySettings: {
        showAnswers: boolean;
        showProfile: boolean;
      };
    };
  } | null;
  relationship: Relationship | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// Define the initial state
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  relationship: null,
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Start loading
    authLoading: (state) => {
      state.loading = true;
      state.error = null;
    },
    // Set authenticated user
    loginSuccess: (
      state,
      action: PayloadAction<{ user: AuthState["user"]; relationship: Relationship | null; token: string }>
    ) => {
      state.user = action.payload.user;
      state.relationship = action.payload.relationship;
      state.token = action.payload.token;
      state.isAuthenticated = true;
      state.loading = false;
      state.error = null;
    },
    // Authentication failed
    authError: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Logout user
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.loading = false;
      state.error = null;
    },
    // Update user profile
    updateUserProfile: (state, action: PayloadAction<AuthState["user"]>) => {
      state.user = action.payload;
    },
  },
});

// Export actions and reducer
export const {
  authLoading,
  loginSuccess,
  authError,
  logout,
  updateUserProfile,
} = authSlice.actions;
export default authSlice.reducer;
