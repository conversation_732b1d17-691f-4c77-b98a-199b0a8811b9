'use client';

import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '../../index';
import { isMobileDevice } from '@/utils/deviceDetection';

interface DeviceState {
  isMobile: boolean;
  isLowPerformance: boolean;
  prefersReducedMotion: boolean;
  shouldOptimizeAnimations: boolean;
  isInitialized: boolean;
}

const initialState: DeviceState = {
  isMobile: false,
  isLowPerformance: false,
  prefersReducedMotion: false,
  shouldOptimizeAnimations: false,
  isInitialized: false,
};

export const deviceSlice = createSlice({
  name: 'device',
  initialState,
  reducers: {
    detectDevice: (state) => {
      if (typeof window !== 'undefined') {
        // Check if the client prefers reduced motion
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        // Check if device is mobile
        const isMobile = isMobileDevice();
        
        // Determine if device is likely low performance
        // Using mobile as a proxy for low performance + checking for reduced motion preference
        const isLowPerformance = isMobile || prefersReducedMotion;
        
        // Should we optimize animations? Yes if low performance or user prefers reduced motion
        const shouldOptimizeAnimations = isLowPerformance || prefersReducedMotion;

        state.isMobile = isMobile;
        state.isLowPerformance = isLowPerformance;
        state.prefersReducedMotion = prefersReducedMotion;
        state.shouldOptimizeAnimations = shouldOptimizeAnimations;
        state.isInitialized = true;
      }
    },
    setOptimizeAnimations: (state, action) => {
      state.shouldOptimizeAnimations = action.payload;
    }
  }
});

// Export actions
export const { detectDevice, setOptimizeAnimations } = deviceSlice.actions;

// Export selectors
export const selectIsMobile = (state: RootState) => state.device.isMobile;
export const selectIsLowPerformance = (state: RootState) => state.device.isLowPerformance;
export const selectShouldOptimizeAnimations = (state: RootState) => state.device.shouldOptimizeAnimations;
export const selectPrefersReducedMotion = (state: RootState) => state.device.prefersReducedMotion;
export const selectIsDeviceInitialized = (state: RootState) => state.device.isInitialized;

// Export reducer
export default deviceSlice.reducer;
