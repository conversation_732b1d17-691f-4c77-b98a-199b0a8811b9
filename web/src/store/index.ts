'use client';

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
// Import our reducer slices
import authReducer from './features/auth/authSlice';
import themeReducer from './features/theme/themeSlice';
import heartsReducer from './features/hearts/heartsSlice';
import questionsReducer from './features/questions/questionsSlice';
import deviceReducer from './features/device/deviceSlice';

// Create the root reducer with all the reducers
const rootReducer = combineReducers({
  // Add your reducer slices here
  auth: authReducer,
  theme: themeReducer,
  hearts: heartsReducer,
  questions: questionsReducer,
  device: deviceReducer,
});

// Configuration for redux-persist
const persistConfig = {
  key: 'root',
  storage,
  // Whitelist (Save specific reducers)
  whitelist: ['auth', 'theme', 'device'],
  // Blacklist (Don't save specific reducers)
  // blacklist: ['someReducer'],
};

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store with middleware and devtools
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Create persistor
export const persistor = persistStore(store);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;