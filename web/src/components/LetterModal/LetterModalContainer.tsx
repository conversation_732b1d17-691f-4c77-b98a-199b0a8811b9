import React, { useEffect, useRef, useState } from "react";
import { LottieDrop, LottieDropHandle } from "./LottieDrop";
import { ReactNode } from "react";

// Reusable Modal with Lottie drop
export interface LetterModalContainerProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
}

export const LetterModalContainer: React.FC<LetterModalContainerProps> = ({
  isOpen,
  onClose,
  children,
}) => {
  const [showContent, setShowContent] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const lottieRef = useRef<LottieDropHandle>(null);
  const [lottieVisible, setLottieVisible] = useState(true);

  useEffect(() => {
    if (isOpen) {
      setIsClosing(false);
      setShowContent(false);
      setLottieVisible(true);
      // Add small delay to ensure DOM has updated before animation starts
      setTimeout(() => {
        lottieRef.current?.play();
      }, 100);
    }
  }, [isOpen]);

  const handleLottieComplete = () => {
    setShowContent(true);
    // Don't hide the lottie element, just make it invisible
    setLottieVisible(false);
  };

  // Handle modal closing with animation
  const handleClose = async () => {
    setIsClosing(true);
    setShowContent(false);
    setLottieVisible(true);
    
    // Animation is now guaranteed to be in the DOM
    if (lottieRef.current) {
      try {
        // Wait for the flyUp animation to complete
        await lottieRef.current.flyUp();
        // Only call onClose after animation is done
      } catch {
        // Fallback in case animation fails - empty catch block is intentional
      } finally {
        setIsClosing(false);
        onClose();
      }
    } else {
      setIsClosing(false);
      onClose();
    }
  };

  // Handle click outside
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Only close if clicking directly on the backdrop, not its children
    if (e.target === e.currentTarget && showContent && !isClosing) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-transparent backdrop-blur-sm flex items-center justify-center z-50"
      onClick={handleBackdropClick}>
      <div className="relative w-full max-w-fit h-full flex items-center justify-center">
        {/* Always render LottieDrop but control visibility */}
        <div style={{ display: lottieVisible ? 'block' : 'none' }}>
          <LottieDrop
            ref={lottieRef}
            onComplete={handleLottieComplete}
            className="mx-auto fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 max-w-screen-sm w-full h-full"
          />
        </div>
        
        {showContent && !isClosing && (
          <div className="bg-[#fffede] p-6 rounded-lg shadow-lg w-full h-fit relative">
            <button
              className="absolute w-8 h-8 top-1 right-2 text-gray-500 hover:text-gray-700 hover:scale-150 transition-transform duration-300 hover:text-red-500 "
              onClick={handleClose}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
            {children}
          </div>
        )}
      </div>
    </div>
  );
};
