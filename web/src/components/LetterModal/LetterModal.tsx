"use client;";

import { useAppSelector } from "@/store/hooks";
import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrambleTextPlugin } from "gsap/ScrambleTextPlugin";

gsap.registerPlugin(ScrambleTextPlugin);

const LetterModal: React.FC = () => {
  const heartNumber = useAppSelector(
    (state) => state.hearts.currentHeartNumber
  );
  const questionHistory = useAppSelector(
    (state) => state.questions.questionHistory
  );
  const content = questionHistory.find((entry) => (new Date(entry.date).getDate()) === heartNumber) || questionHistory[0];
  const partnerAnswerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (partnerAnswerRef.current) {
      // Get the text to display
      const text = content?.partnerAnswer || "Not answered yet";

      // Calculate duration based on average reading speed (15 chars per second)
      const readingSpeed = 15; // characters per second
      const calculatedDuration = Math.max(0.8, text.length / readingSpeed);

      gsap.fromTo(
        partnerAnswerRef.current,
        {
          scrambleText: {
            text: "",
            chars: "upperAndLowerCase",
            revealDelay: 0.5,
          },
        },
        {
          duration: calculatedDuration,
          scrambleText: {
            text: text,
            chars: "upperAndLowerCase",
            speed: 0.4,
            revealDelay: 0.5,
          },
          ease: "power2.out",
        }
      );
    }
  }, [content.partnerAnswer]);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center bg-[#fffca5] rounded-lg">
      <h1 className="italic text-xl font-semibold text-center text-black">
        Heart {heartNumber}
      </h1>
      <div className="p-6 bg-[#fffede] rounded-xl shadow-xl max-w-lg mx-auto space-y-4 text-gray-800">
        <div className="text-sm text-gray-500">
          {new Date(content.date).toDateString()}
        </div>

        <div>
          <p className="mt-1 italic text-gray-600">“{content.question.text}”</p>
          <p className="text-xs mt-1 text-gray-400">
            Category: {content.question.category}
          </p>
        </div>

        <div>
          <h3 className="font-medium text-gray-700">Your Answer</h3>
          <p className="p-2 rounded mt-1 text-sm">
            {content.userAnswer}
          </p>
        </div>

        <div>
          <h3 className="font-medium text-gray-700">Partner&apos;s Answer</h3>
          <div
            ref={partnerAnswerRef}
            className={`${content.bothAnswered ? "bg-green-100" : "bg-red-100 text-red-800"} p-2 rounded mt-1 text-sm font-mono font-semibold text-green-800`}>
            {/* Scrambled text will animate in here */}
          </div>
        </div>

        {content.bothAnswered && (
          <div className="text-green-600 text-xs text-right italic">
            ✓ Both answered
          </div>
        )}
      </div>
    </div>
  );
};

export default LetterModal;
