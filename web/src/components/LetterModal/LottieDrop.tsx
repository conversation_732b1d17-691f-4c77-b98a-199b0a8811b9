import React, {
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useState,
} from "react";
import lottie, { AnimationItem } from "lottie-web";
import { gsap } from "gsap";

export interface LottieDropProps {
  className?: string;
}

export interface LottieDropHandle {
  play: () => void;
  pauseAtEnd: () => void;
  flyUp: () => Promise<void>; // New method for reverse animation
}

const LottieDropInner = forwardRef<
  LottieDropHandle,
  LottieDropProps & { onComplete?: () => void }
>(
  (
    { className, onComplete },
    ref
  ) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const animRef = useRef<AnimationItem | null>(null);
    const [isDropped, setIsDropped] = useState(false);

    // Initialize lottie
    useEffect(() => {
      if (containerRef.current) {
        animRef.current = lottie.loadAnimation({
          container: containerRef.current,
          renderer: "svg",
          loop: false,
          autoplay: false,
          path: "/animations/envelope-animation.json",
        });

        animRef.current.setSpeed(3);

        // call onComplete when animation done
        animRef.current.addEventListener("complete", () => {
          if (onComplete) {
            onComplete();
          }
        });
      }
      return () => {
        animRef.current?.destroy();
        animRef.current = null;
      };
    }, [onComplete]);

    // Expose controls
    useImperativeHandle(ref, () => ({
      play() {
        // First do the drop animation, then the lottie animation will play after drop
        if (containerRef.current && !isDropped) {
          gsap.fromTo(
            containerRef.current,
            { y: -3000, opacity: 1 },
            {
              y: 0,
              duration: 1,
              ease: "bounce.out",
              onComplete: () => {
                // play Lottie after drop
                animRef.current?.play();
                setIsDropped(true);
              },
            }
          );
        } else {
          // If already dropped, just play the Lottie animation
          animRef.current?.goToAndPlay(0, true);
        }
      },
      pauseAtEnd() {
        if (animRef.current) {
          const totalFrames = animRef.current.getDuration(true);
          animRef.current.goToAndStop(totalFrames, true);
        }
      },
      // New method for reverse animation (fly up)
      flyUp() {
        return new Promise<void>((resolve) => {
          if (containerRef.current) {
            gsap.fromTo(
              containerRef.current,
              { y: 0, opacity: 1 },
              {
                y: -3000,
                duration: 0.8, // Slightly faster than drop animation
                ease: "power2.in", // A nice easing for flying up
                onComplete: () => {
                  setIsDropped(false);
                  resolve();
                },
              }
            );
          } else {
            resolve();
          }
        });
      },
    }));

    return (
      <div
      data-cy="lottie-drop"
        ref={containerRef}
        className={`${className || ''} transition-opacity duration-300`}
      />
    );
  }
);

LottieDropInner.displayName = "LottieDrop";
export const LottieDrop = LottieDropInner;
