export const depthFragmentShader = `
    #include <packing>
    varying vec2 vUv;
    uniform sampler2D tDiffuse;
    uniform sampler2D tDepth;
    uniform float cameraNear;
    uniform float cameraFar;

    float readDepth(sampler2D depthSampler, vec2 coord) {
        float fragCoordZ = texture2D(depthSampler, coord).x;
        float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);
        return viewZToOrthographicDepth(viewZ, cameraNear, cameraFar);
    }

    void main() {
        float depth = readDepth(tDepth, vUv);
        
        // Define colors for near and far objects (blackish scheme)
        vec3 nearColor = vec3(1.0, 0.6, 1.0);  // orange 
        vec3 farColor = vec3(0.0, 0.0, 0.0);      // pure black
        
        // Mix the colors based on depth
        vec3 color = mix(nearColor, farColor, depth);
        
        gl_FragColor = vec4(color, 1.0);
    }
`;