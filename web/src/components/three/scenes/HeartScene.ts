import * as THREE from 'three';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';
import { FontLoader, Font } from 'three/examples/jsm/loaders/FontLoader.js';
import { TextGeometry, TextGeometryParameters } from 'three/examples/jsm/geometries/TextGeometry.js';
import { HeartGeometry } from '../geometry';

interface SceneOptions {
  count: number;
  onHeartClick: (heartNumber: number) => void;
  heartNumbers: number[];
  theme?: 'light' | 'dark';
}

export class HeartScene {
  scene: THREE.Scene;
  geometry: HeartGeometry; // Changed from TorusKnotGeometry to HeartGeometry
  material: THREE.MeshPhysicalMaterial;
  envMap: THREE.Texture | null = null;
  meshes: THREE.Group[] = []; // Changed from Mesh[] to Group[] to hold both heart and numbers
  rotationSpeed: number = 0.005; // Add rotation for more dynamic appearance
  font: Font | null = null;
  raycaster: THREE.Raycaster; // Added raycaster for click detection
  onHeartClick: (heartNumber: number) => void;
  heartNumbers: number[];
  theme: 'light' | 'dark';

  constructor({ count, onHeartClick, heartNumbers, theme = 'dark' }: SceneOptions) {
    this.heartNumbers = heartNumbers;
    this.onHeartClick = onHeartClick;
    this.theme = theme;
    this.scene = new THREE.Scene();
    // Add fog to the scene with color based on theme
    const fogColor = theme === 'light' ? 0xd0dbf1 : 0x000000;
    this.scene.fog = new THREE.Fog(fogColor, 5, 21);
    
    // Initialize raycaster
    this.raycaster = new THREE.Raycaster();
    
    // Replace TorusKnotGeometry with our custom HeartGeometry
    this.geometry = new HeartGeometry(1, 64, 0.5, 0.12, 0.07, 12);
    
    // Use MeshPhysicalMaterial for enhanced visual quality
    // Adjust material properties based on theme
    const heartBaseColor = this.theme === 'light' ? 0xe91e63 : 0xf44336; // Brighter pink for light theme
    const metalness = this.theme === 'light' ? 0.5 : 0.7; // Less metallic in light theme for better contrast
    const roughness = this.theme === 'light' ? 0.1 : 0.07; // Slightly more roughness in light theme
    
    this.material = new THREE.MeshPhysicalMaterial({ 
      color: heartBaseColor,
      metalness: metalness,
      roughness: roughness,
      clearcoat: 1.0,       // Maximum clearcoat for a wet/polished look
      clearcoatRoughness: 0.1,
      envMapIntensity: this.theme === 'light' ? 1.5 : 2.0, // Adjust for light theme
      reflectivity: 1.0,
      side: THREE.DoubleSide  
    });
    
    // Load HDR environment map
    const rgbeLoader = new RGBELoader();
    rgbeLoader.setPath('/env/');
    rgbeLoader.load('sunrise_1k.hdr', (texture) => {
      // Set higher quality mapping for better reflections
      texture.mapping = THREE.EquirectangularReflectionMapping;
      
      // Make HDR texture available to the entire scene for better reflections
      this.scene.environment = texture;
      this.envMap = texture;
      
      // Update material with the loaded environment map
      this.material.envMap = texture;
      this.material.needsUpdate = true;
      
      // Also refresh the scene to ensure all meshes reflect the updated material
      this.refreshScene(count);
    });
    
    // Load font for embossed numbers
    const fontLoader = new FontLoader();
    fontLoader.load('/fonts/helvetiker_regular.typeface.json', (font) => {
      this.font = font;
      // Refresh scene once the font is loaded
      if (this.meshes.length === 0) {
        this.populateScene(count);
      } else {
        this.refreshScene(count);
      }
    });
    
    // Initial population of the scene
    this.populateScene(count);
    
    // Enhanced lighting setup for better metallic highlights - adjusted for theme
    
    // Add ambient light for base illumination - brighter in light theme
    const ambientLight = new THREE.AmbientLight(0xffffff, this.theme === 'light' ? 0.5 : 0.3);
    this.scene.add(ambientLight);
    
    // Add directional light for highlights - adjusted for theme
    const directionalLight = new THREE.DirectionalLight(0xffffff, this.theme === 'light' ? 1.8 : 1.5);
    directionalLight.position.set(5, 5, 5);
    this.scene.add(directionalLight);
    
    // Add point light for extra highlights on the opposite side
    // Different color temperature for light theme
    const pointLightColor = this.theme === 'light' ? 0xffaa44 : 0xff9000; // Warmer in light theme
    const pointLight = new THREE.PointLight(pointLightColor, this.theme === 'light' ? 1.2 : 1.5, 20);
    pointLight.position.set(-5, 2, 3);
    this.scene.add(pointLight);
    
    // Add a second point light for even more highlights
    // More cyan in light theme for nice contrast with the pink hearts
    const pointLight2Color = this.theme === 'light' ? 0x00aaff : 0x0088ff;
    const pointLight2 = new THREE.PointLight(pointLight2Color, this.theme === 'light' ? 0.8 : 1.0, 15);
    pointLight2.position.set(3, -5, 2);
    this.scene.add(pointLight2);
  }

  // Create a heart with embossed number
  createHeartWithNumber(heartNumber: number, scale: number = 1, isLastHeart: boolean = false): THREE.Group {
    // Create a group to hold the heart and number meshes
    const group = new THREE.Group();
    
    // Create the heart mesh with the existing material
    const heartMesh = new THREE.Mesh(this.geometry, this.material.clone());
    // Apply more color variation for hearts based on theme
    const hue = Math.random() * 0.1; // Small hue shift
    
    // Special handling for the last heart to make it stand out
    let baseColorHex;
    if (isLastHeart) {
      // Last heart is highlighted in a special color based on theme
      baseColorHex = this.theme === 'light' ? 0xf50057 : 0xff1744; // More vibrant in both themes
    } else {
      // Regular hearts use theme-specific base color
      baseColorHex = this.theme === 'light' ? 0xe91e63 : 0xf44336; // Brighter pink for light theme
    }
    
    const color = new THREE.Color(baseColorHex); 
    
    // Adjust color variation ranges based on theme
    const saturationVar = this.theme === 'light' ? 0.1 : 0;
    const brightnessVar = isLastHeart ? 0.1 : (this.theme === 'light' ? 0.15 : 0.2);
    color.offsetHSL(hue, saturationVar, Math.random() * brightnessVar); 
    heartMesh.material.color = color;
    group.add(heartMesh);
    
    // Store the number for later retrieval
    group.userData.number = heartNumber;
    
    // Add embossed numbers if font is loaded
    if (this.font) {
      // Create text geometry for the number
      const textOptions: TextGeometryParameters = {
        font: this.font,
        size: 0.5 ,        // scale relative to heart
        depth: 0.05 * scale,     // how much it "pops" off the face
        curveSegments: 8,
        bevelEnabled: true,       // bevel for the text
        bevelThickness: 0.01 * scale,
        bevelSize: 0.005 * scale,
        bevelSegments: 3,
      };
      
      const textGeometry = new TextGeometry(heartNumber?.toString() || "1", textOptions);
      textGeometry.computeBoundingBox();
      textGeometry.center(); // Center the text
      
      // Material for the embossed number - theme-aware
      const textColor = isLastHeart 
        ? 0xff0000  // Always bright red for the last heart
        : (this.theme === 'light' ? 0x333333 : 0xffffe0);  // Dark in light theme, light yellow in dark theme
      
      const textMaterial = new THREE.MeshStandardMaterial({
        color: textColor,
        metalness: this.theme === 'light' ? 0.5 : 0.7,
        roughness: this.theme === 'light' ? 0.4 : 0.3,
        side: THREE.DoubleSide,
      });

      // Front text
      // const frontText = new THREE.Mesh(textGeometry, textMaterial);
      // frontText.position.z = 0.8/2 + (textOptions?.depth || 0.05 * scale)/2 + 0.005; // Position just above the front face
      // frontText.rotateX(Math.PI/2); // Rotate to face upward
      // group.add(frontText);
      
      // Back text - clone and mirror
      const backText = new THREE.Mesh(textGeometry, textMaterial);
      backText.position.z = -(0.4/2 + (textOptions?.depth || 0.05 * scale)/2 + 0.005); // Position just above the back face
      backText.rotateY(Math.PI); // Rotate to face outward
      backText.rotateZ(Math.PI); // Rotate to face upward
      group.add(backText);
    }
    
    return group;
  }

  // Refresh the scene when environment map loads or theme changes
  refreshScene(count: number, newTheme?: 'light' | 'dark') {
    // Update theme if provided
    if (newTheme && newTheme !== this.theme) {
      this.theme = newTheme;
      
      // Update fog color based on theme
      const fogColor = this.theme === 'light' ? 0xd0dbf1 : 0x000000;
      if (this.scene.fog) {
        (this.scene.fog as THREE.Fog).color.set(fogColor);
      }
      
      // Update material properties for the theme
      const heartBaseColor = this.theme === 'light' ? 0xe91e63 : 0xf44336;
      this.material.color.set(heartBaseColor);
      this.material.metalness = this.theme === 'light' ? 0.5 : 0.7;
      this.material.roughness = this.theme === 'light' ? 0.1 : 0.07;
      this.material.envMapIntensity = this.theme === 'light' ? 1.5 : 2.0;
      this.material.needsUpdate = true;
    }
    
    // Force a rebuild of all meshes with the updated material
    this.clearScene();
    this.populateScene(count);
  }

  // Clear all meshes from the scene
  clearScene() {
    this.meshes.forEach(group => {
      this.scene.remove(group);
    });
    this.meshes = [];
  }

  // Add an update method to animate the scene
  update() {
    // Rotate each heart for more dynamic appearance
    this.meshes.forEach(group => {
      group.rotation.x += this.rotationSpeed * Math.random();
      group.rotation.y += this.rotationSpeed * Math.random();
    });
  }

  // Handle click detection
  handleMouseClick(normalizedMouseX: number, normalizedMouseY: number, camera: THREE.Camera): void {
    // Set the raycaster with normalized device coordinates
    this.raycaster.setFromCamera(new THREE.Vector2(normalizedMouseX, normalizedMouseY), camera);
    
    // Check for intersections with heart meshes
    const intersects = this.raycaster.intersectObjects(this.meshes, true);
    
    if (intersects.length > 0) {
      // Find the parent group (heart) that was clicked
      let heartGroup: THREE.Object3D | null = intersects[0].object;
      
      // Traverse up to find the parent group that has userData.number
      while (heartGroup && !heartGroup.userData.number && heartGroup.parent) {
        heartGroup = heartGroup.parent;
      }
      
      // If we found a heart group with a number, log it
      if (heartGroup && heartGroup.userData.number !== undefined) {
        this.onHeartClick(heartGroup.userData.number);
      }
    }
  }

  populateScene(count: number) {
    // Calculate scaling factor based on count
    // As count increases, the distribution area grows logarithmically
    const scaleFactor = 5 * (1 + Math.log10(Math.max(1, count / 10)));
    
    // Add new meshes
    for (let i = 0; i < count; i++) {
      const r = Math.random() * Math.PI * 2;
      const z = Math.random() * 2 - 1;
      const zScale = Math.sqrt(1 - z * z) * scaleFactor;
      
      const heartNumber = this.heartNumbers[i];
      
      // Randomize scale for more interesting visual
      const scale = 0.5 + Math.random() * 1;
      
      // Create a heart with embossed number
      const heartGroup = this.createHeartWithNumber(heartNumber, scale, i === count - 1);
      
      // Position the group
      heartGroup.position.set(
        Math.cos(r) * zScale,
        Math.sin(r) * zScale,
        z * scaleFactor
      );
      heartGroup.rotation.set(Math.random(), Math.random(), Math.random());
      heartGroup.scale.set(scale, scale, scale);
      
      // Ensure groups don't overlap by checking minimum distance
      let tooClose = false;
      const minDistance = 2.5; // Minimum distance between groups
      
      for (const existing of this.meshes) {
        if (existing !== heartGroup) {
          const distance = heartGroup.position.distanceTo(existing.position);
          if (distance < minDistance) {
            tooClose = true;
            break;
          }
        }
      }
      
      // Only add if not too close to another group
      if (!tooClose || i < 10) { // Always add at least 10 groups
        this.scene.add(heartGroup);
        this.meshes.push(heartGroup);
      } else {
        // Try again with different position (decrement i to reuse the loop iteration)
        i--;
      }
    }
  }

  dispose() {
    this.clearScene();
    this.geometry.dispose();
    this.material.dispose();
    if (this.envMap) this.envMap.dispose();
  }
}