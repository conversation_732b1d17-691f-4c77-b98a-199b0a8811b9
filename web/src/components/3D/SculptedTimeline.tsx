import React, { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { HeartIcon } from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { selectIsMobile } from "@/store/features/device/deviceSlice";
import { useAppSelector } from "@/store/hooks";

interface TimelineData {
  year: number;
  months: number[];
}

interface SculptedTimelineProps {
  data?: TimelineData[];
  onDateSelect?: (year: number, month: number) => void;
  selectedYear?: number;
  selectedMonth?: number;
}

const SculptedTimeline: React.FC<SculptedTimelineProps> = ({
  data = [
    { year: 2024, months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] },
    { year: 2023, months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] },
    { year: 2022, months: [6, 7, 8, 9, 10, 11, 12] },
  ],
  onDateSelect = (year: number, month: number) => {
    console.log("Selected:", year, month);
  },
  selectedYear = 2024,
   
  selectedMonth = 1,
}) => {
  const [orbExpanded, setOrbExpanded] = useState(false);
  const [orbState, setOrbState] = useState<"initial" | "years" | "months">(
    "initial"
  );
  const [tempSelectedYear, setTempSelectedYear] = useState<number | null>(null);
  const [confirmationText, setConfirmationText] = useState("");
  const isMobile = useAppSelector(selectIsMobile);
  const orbRef = useRef<HTMLDivElement>(null);

  const handleYearSelect = (year: number) => {
    console.log("Year selected:", year);
    setTempSelectedYear(year);
    setOrbState("months");
  };

  const handleMonthSelect = (month: number) => {
    const year = tempSelectedYear || selectedYear;
    console.log("Month selected:", month, "for year:", year);
    onDateSelect(year, month);

    const monthName = getMonthNameFull(month);
    setConfirmationText(`${monthName} ${year}`);
    setOrbExpanded(false);
    setOrbState("initial");

    // Hide confirmation after 2 seconds
    setTimeout(() => setConfirmationText(""), 2000);
  };

  const handleOrbClick = () => {
    console.log("Orb clicked, current state:", orbState);
    if (orbState === "initial") {
      setOrbExpanded(true);
      setOrbState("years");
    } else if (orbState === "months") {
      setOrbExpanded(false);
      setOrbState("initial");
    }
  };

  const getMonthNameFull = (month: number) => {
    const names = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    return names[month - 1];
  };

  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement>) => {
    const ele = document.getElementById("sculpted-timeline");
    if (ele && !ele.contains(event.target as Node)) {
      setOrbExpanded(false);
      setOrbState("initial");
    }
  };

  return (
    <>
      <div
        id="sculpted-timeline"
        className={`fixed left-6 ${
          isMobile
            ? orbExpanded
              ? "bottom-0 transform -translate-y-10"
              : "bottom-15 transform translate-y-1/2"
            : "top-1/2 transform -translate-y-1/2"
        } z-5`}>
        {/* Growth Orb */}
        <motion.div
          ref={orbRef}
          className="relative cursor-pointer"
          onClick={handleOrbClick}
          animate={{
            width: orbExpanded ? 280 : 80,
            height: orbExpanded ? 280 : 80,
          }}
          transition={{ duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
          style={{
            width: orbExpanded ? 280 : 80,
            height: orbExpanded ? 280 : 80,
          }}>
          {/* Orb Background with Pulsing */}
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-white/30 shadow-2xl"
            style={{
              background:
                "radial-gradient(circle at 30% 30%, rgba(139, 69, 199, 0.4), rgba(219, 39, 119, 0.3), rgba(59, 130, 246, 0.2))",
              backdropFilter: "blur(12px)",
            }}
            animate={
              orbExpanded
                ? { scale: 1 }
                : {
                    scale: [1, 1.1, 1],
                    boxShadow: [
                      "0 0 20px rgba(139, 69, 199, 0.3)",
                      "0 0 40px rgba(139, 69, 199, 0.5)",
                      "0 0 20px rgba(139, 69, 199, 0.3)",
                    ],
                  }
            }
            transition={
              orbExpanded
                ? { duration: 0.6 }
                : { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }
          />

          {/* Initial Heart Icon */}
          <AnimatePresence>
            {orbState === "initial" && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                transition={{ duration: 0.3 }}>
                <HeartIcon className="w-10 h-10 text-white drop-shadow-lg" />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Years Display */}
          <AnimatePresence>
            {orbState === "years" && (
              <motion.div
                className="absolute inset-0"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.4 }}>
                {data.map((item, index) => {
                  const angle =
                    (index / data.length) * 2 * Math.PI - Math.PI / 2;
                  const radius = 90;
                  const x = Math.cos(angle) * radius + 140; // Center offset
                  const y = Math.sin(angle) * radius + 140; // Center offset

                  return (
                    <motion.div
                      key={item.year}
                      className="absolute w-16 h-16 cursor-pointer"
                      style={{
                        left: x - 32,
                        top: y - 32,
                      }}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: index * 0.15, duration: 0.5 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleYearSelect(item.year);
                      }}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.8 }}>
                     { item.year === selectedYear && (
                        <HeartIconSolid className="absolute top-0 right-0 transform rotate-45 z-2 w-4 h-4 text-red-500 drop-shadow-lg" />
                      )}
                      <div
                        className={`w-full h-full rounded-full border-2 flex items-center justify-center shadow-xl`}
                        style={{
                          background:
                            "linear-gradient(135deg, rgba(96, 165, 250, 0.6), rgba(147, 197, 253, 0.4))",
                          backdropFilter: "blur(8px)",
                          borderColor: item.year === selectedYear
                            ? "red"
                            : "rgba(255, 255, 255, 0.4)",
                        }}>
                        <span className="text-white text-sm font-bold drop-shadow-lg">
                          {item.year}
                        </span>
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Months Display */}
          <AnimatePresence>
            {orbState === "months" && tempSelectedYear && (
              <motion.div
                className="absolute inset-0"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.4 }}>
                {/* Center Year Label */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold drop-shadow-lg">
                    {tempSelectedYear}
                  </span>
                </div>

                {/* Month Heart Fragments */}
                {data
                  .find((d) => d.year === tempSelectedYear)
                  ?.months.map((month, index) => {
                    const totalMonths =
                      data.find((d) => d.year === tempSelectedYear)?.months
                        .length || 1;
                    const angle =
                      (index / totalMonths) * 2 * Math.PI - Math.PI / 2;
                    const radius = 80;
                    const x = Math.cos(angle) * radius + 140; // Center offset
                    const y = Math.sin(angle) * radius + 140; // Center offset

                    return (
                      <motion.div
                        key={month}
                        className="absolute w-12 h-12 cursor-pointer"
                        style={{
                          left: x - 24,
                          top: y - 24,
                        }}
                        initial={{ scale: 0, opacity: 0, rotate: -180 }}
                        animate={{ scale: 1, opacity: 1, rotate: 0 }}
                        transition={{ delay: index * 0.08, duration: 0.4 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMonthSelect(month);
                        }}
                        whileHover={{ scale: 1.2 }}
                        whileTap={{ scale: 0.8 }}>
                        <div
                          className="w-full h-full rounded-lg border-2 border-white/40 flex flex-col items-center justify-center shadow-lg transform rotate-12"
                          style={{
                            background:
                              "linear-gradient(135deg, rgba(236, 72, 153, 0.6), rgba(251, 113, 133, 0.4))",
                            backdropFilter: "blur(6px)",
                          }}>
                          { tempSelectedYear === selectedYear && month === selectedMonth ? (
                            <HeartIconSolid className="w-4 h-4 text-red-500 mb-1" />
                          ) : <HeartIcon className="w-4 h-4 text-white mb-1" />}
                          <span className="text-white text-xs font-bold">
                            {month.toString().padStart(2, "0")}
                          </span>
                        </div>
                      </motion.div>
                    );
                  })}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Confirmation Label */}
        <AnimatePresence>
          {confirmationText && (
            <motion.div
              className="absolute -top-16 left-1/2 transform -translate-x-1/2"
              initial={{ opacity: 0, y: 20, scale: 0.8 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.8 }}
              transition={{ duration: 0.3 }}>
              <div
                className="px-4 py-2 rounded-lg text-white text-sm font-medium shadow-lg border border-white/20"
                style={{
                  background: "rgba(0, 0, 0, 0.8)",
                  backdropFilter: "blur(8px)",
                }}>
                {confirmationText}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      {orbExpanded && (
        <div
          className="fixed top-0 left-0 w-screen h-screen z-3"
          onClick={handleClickOutside}></div>
      )}
    </>
  );
};

export default SculptedTimeline;
