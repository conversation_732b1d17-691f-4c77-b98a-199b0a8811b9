import React from "react";

interface ClusterSelectorProps {
  month: number;
  year: number;
  setMonth: (month: number) => void;
  setYear: (year: number) => void;
}

const ClusterSelector: React.FC<ClusterSelectorProps> = ({
  month,
  year,
  setMonth,
  setYear,
}) => {
  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setMonth(parseInt(e.target.value));
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setYear(parseInt(e.target.value));
  };

  return (
    <div className="fixed left-[50%] translate-x-[-50%] top-4 flex items-center wrap space-x-4">
      <div>
        <label
          htmlFor="month"
          className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Month:
        </label>
        <select
          id="month"
          name="month"
          value={month}
          onChange={handleMonthChange}
          className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          {Array.from({ length: 12 }, (_, i) => (
            <option className="text-black" key={i + 1} value={i + 1}>
              {new Date(0, i, 10).toLocaleString("default", { month: "long" })}
            </option>
          ))}
        </select>
      </div>
      <div>
        <label
          htmlFor="year"
          className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Year:
        </label>
        <select
          id="year"
          name="year"
          value={year}
          onChange={handleYearChange}
          className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          {Array.from({ length: 10 }, (_, i) => (
            <option
              className="text-black"
              key={i + 1}
              value={new Date().getFullYear() - i}>
              {new Date().getFullYear() - i}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default ClusterSelector;
