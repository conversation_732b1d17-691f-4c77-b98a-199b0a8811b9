'use client';

import { useState, useEffect } from 'react';
import { getAnswerHistory } from '@/utils/api';

interface HistoryEntry {
  date: string;
  question: {
    id: string;
    text: string;
    category: string;
    inputType: string;
  };
  userAnswer: string | null;
  partnerAnswer: string | null;
  bothAnswered: boolean;
}

export default function AnswerHistory() {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setLoading(true);
        console.log('AnswerHistory.jsx: fetchHistory');
        const response = await getAnswerHistory();
        
        // Sort history by date, most recent first
        const sortedHistory = response.history.sort((a: HistoryEntry, b: HistoryEntry) => {
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        });
        
        setHistory(sortedHistory);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (err: any) {
        console.error('Error fetching history:', err);
        setError(err.response?.data?.error || 'Failed to load answer history');
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
        <p>{error}</p>
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg text-center">
        <p>No question history yet. Start answering daily questions to build your history!</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <h2 className="text-xl font-bold mb-4">Past Questions & Answers</h2>
      
      <div className="space-y-6">
        {history.map((entry, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="mb-2 text-sm text-gray-500">
              {new Date(entry.date).toLocaleDateString(undefined, { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
            
            <div className="mb-4">
              <span className="inline-block px-2 py-1 text-xs rounded bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mb-2">
                {entry.question.category.charAt(0).toUpperCase() + entry.question.category.slice(1)}
              </span>
              <h3 className="text-lg font-semibold">{entry.question.text}</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <h4 className="text-sm font-semibold mb-2">Your Answer</h4>
                <p className="whitespace-pre-wrap text-sm">{entry.userAnswer || "Not answered"}</p>
              </div>
              
              <div className="p-3 bg-blue-50 dark:bg-gray-700 rounded-md">
                <h4 className="text-sm font-semibold mb-2">Partner&apos;s Answer</h4>
                <p className="whitespace-pre-wrap text-sm">
                  {entry.partnerAnswer ? entry.bothAnswered ? entry.partnerAnswer : "Not visible yet" : "Not answered yet" }
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}