'use client';

import { useState, useEffect } from 'react';
import { getAnswerHistory } from '@/utils/api';
import { useAppSelector } from '@/store/hooks';

interface HistoryEntry {
  date: string;
  question: {
    id: string;
    text: string;
    category: string;
    inputType: string;
  };
  userAnswer: string | null;
  partnerAnswer: string | null;
  bothAnswered: boolean;
}

export default function AnswerHistory() {
  const { relationship } = useAppSelector((state) => state.auth);
  const initYear = relationship?.activatedAt
    ? new Date(relationship.activatedAt).getFullYear()
    : new Date().getFullYear();
  const initMonth = relationship?.activatedAt
    ? new Date(relationship.activatedAt).getMonth() + 1
    : new Date().getMonth() + 1;

  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  
  // Generate years array from relationship activation year to current year
  const years = Array.from(
    { length: currentYear - initYear + 1 },
    (_, i) => initYear + i
  );
  
  // Generate months array based on selected year
  const getMonths = () => {
    if (selectedYear === currentYear) {
      // For current year, only show months up to current month
      return Array.from({ length: currentMonth }, (_, i) => i + 1);
    } else if (selectedYear === initYear) {
      // For first year, only show months from activation month
      return Array.from(
        { length: 12 - initMonth + 1 },
        (_, i) => initMonth + i
      );
    } else {
      // For other years, show all months
      return Array.from({ length: 12 }, (_, i) => i + 1);
    }
  };

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setLoading(true);
        console.log('AnswerHistory.jsx: fetchHistory', { month: selectedMonth, year: selectedYear });
        const response = await getAnswerHistory(selectedMonth, selectedYear);
        
        // Sort history by date, most recent first
        const sortedHistory = response.history.sort((a: HistoryEntry, b: HistoryEntry) => {
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        });
        
        setHistory(sortedHistory);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (err: any) {
        console.error('Error fetching history:', err);
        setError(err.response?.data?.error || 'Failed to load answer history');
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, [selectedMonth, selectedYear]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex flex-wrap justify-between items-center mb-6">
        <h2 className="text-xl font-bold mb-2 md:mb-0">Past Questions & Answers</h2>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center">
            <label htmlFor="month-select" className="mr-2 text-sm font-medium">Month:</label>
            <select
              id="month-select"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(Number(e.target.value))}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {getMonths().map((month) => (
                <option key={`month-${month}`} value={month}>
                  {new Date(0, month - 1).toLocaleString('default', { month: 'long' })}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center">
            <label htmlFor="year-select" className="mr-2 text-sm font-medium">Year:</label>
            <select
              id="year-select"
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {years.map((year) => (
                <option key={`year-${year}`} value={year}>{year}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      { history.length !== 0 ? (<div className="space-y-6">
        {history.map((entry, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="mb-2 text-sm text-gray-500">
              {new Date(entry.date).toLocaleDateString(undefined, { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
            
            <div className="mb-4">
              <span className="inline-block px-2 py-1 text-xs rounded bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mb-2">
                {entry.question.category.charAt(0).toUpperCase() + entry.question.category.slice(1)}
              </span>
              <h3 className="text-lg font-semibold">{entry.question.text}</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <h4 className="text-sm font-semibold mb-2">Your Answer</h4>
                <p className="whitespace-pre-wrap text-sm">{entry.userAnswer || "Not answered"}</p>
              </div>
              
              <div className="p-3 bg-blue-50 dark:bg-gray-700 rounded-md">
                <h4 className="text-sm font-semibold mb-2">Partner&apos;s Answer</h4>
                <p className="whitespace-pre-wrap text-sm">
                  {entry.partnerAnswer ? entry.bothAnswered ? entry.partnerAnswer : "Not visible yet" : "Not answered yet" }
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>) : (
        <div className="p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded mb-4">
          <p>No history available for this month.</p>
        </div>
      ) }
    </div>
  );
}