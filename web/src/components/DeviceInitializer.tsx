'use client';

import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { detectDevice, selectIsDeviceInitialized } from '@/store/features/device/deviceSlice';

export function DeviceInitializer() {
  const dispatch = useAppDispatch();
  const isInitialized = useAppSelector(selectIsDeviceInitialized);

  useEffect(() => {
    if (!isInitialized) {
      dispatch(detectDevice());
    }
  }, [dispatch, isInitialized]);

  return null; // This component doesn't render anything
}
