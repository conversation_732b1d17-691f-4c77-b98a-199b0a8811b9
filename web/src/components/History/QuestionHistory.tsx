import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDownIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  XMarkIcon,
  SunIcon,
  MoonIcon,
  HeartIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useAppSelector } from '@/store/hooks';
import { getAnswerHistory } from '@/utils/api';

type TimeOfDay = 'morning' | 'evening';
type QuestionStatus = 'user_answered_waiting' | 'partner_answered_waiting' | 'both_answered_hidden' | 'both_answered_visible';

interface QuestionData {
  id: number;
  date: string;
  day: number;
  question: string;
  timeOfDay: TimeOfDay;
  status: QuestionStatus;
  userAnswer: string | null;
  partnerAnswer: string | null;
}

interface MonthData {
  [month: string]: QuestionData[];
}

interface YearData {
  [year: string]: MonthData;
}

// Mapped interface for API response
interface HistoryEntry {
  date: string;
  question: {
    id: string;
    text: string;
    category: string;
    inputType: string;
  };
  userAnswer: string | null;
  partnerAnswer: string | null;
  bothAnswered: boolean;
}

const QuestionHistory: React.FC = () => {
  const { relationship } = useAppSelector((state) => state.auth);
  const initYear = relationship?.activatedAt
    ? new Date(relationship.activatedAt).getFullYear()
    : new Date().getFullYear();
  const initMonth = relationship?.activatedAt
    ? new Date(relationship.activatedAt).getMonth() + 1
    : new Date().getMonth() + 1;
  
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  
  const [selectedYear, setSelectedYear] = useState<number>(currentYear);
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date(0, currentMonth - 1).toLocaleString('default', { month: 'long' }));
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [selectedAnswerOverlay, setSelectedAnswerOverlay] = useState<QuestionData | null>(null);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const contentRef = useRef<HTMLDivElement | null>(null);
  
  const [questions, setQuestions] = useState<QuestionData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [yearMonthData, setYearMonthData] = useState<YearData>({});
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [availableMonthsByYear, setAvailableMonthsByYear] = useState<{ [year: string]: number[] }>({});

  // Use available years from API if available, otherwise use calculated years
  const years = availableYears.length > 0
    ? availableYears.sort((a, b) => b - a)
    : relationship?.activatedAt
      ? Array.from(
          { length: currentYear - initYear + 1 },
          (_, i) => initYear + i
        ).sort((a, b) => b - a)
      : [];
  
  // Get available months for the selected year
  const getAvailableMonths = (year: number): string[] => {
    // If we have API data for this year, use it
    if (availableMonthsByYear[year] && availableMonthsByYear[year].length > 0) {
      return availableMonthsByYear[year].map(monthNum => 
        new Date(0, monthNum - 1).toLocaleString('default', { month: 'long' })
      );
    }
    
    // If we have cached data for this year
    if (yearMonthData[String(year)] && Object.keys(yearMonthData[String(year)]).length > 0) {
      return Object.keys(yearMonthData[String(year)]);
    }
    
    // Fallback to calculated months
    if (year === currentYear) {
      // For current year, only show months up to current month
      return Array.from({ length: currentMonth }, (_, i) => 
        new Date(0, i).toLocaleString('default', { month: 'long' })
      );
    } else if (year === initYear) {
      // For first year, only show months from activation month
      return Array.from(
        { length: 12 - initMonth + 1 },
        (_, i) => new Date(0, initMonth + i - 1).toLocaleString('default', { month: 'long' })
      );
    } else {
      // For other years, show all months
      return Array.from({ length: 12 }, (_, i) => 
        new Date(0, i).toLocaleString('default', { month: 'long' })
      );
    }
  };
  
  const months = getAvailableMonths(selectedYear);
  
  // Convert month name to number
  const getMonthNumber = (monthName: string): number => {
    return new Date(`${monthName} 1, 2000`).getMonth() + 1;
  };

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Fetch question data when year or month changes
  useEffect(() => {
    const fetchQuestionHistory = async () => {
      try {
        setLoading(true);
        const monthNumber = getMonthNumber(selectedMonth);
        
        console.log('QuestionHistory: fetchHistory', { month: monthNumber, year: selectedYear });
        const response = await getAnswerHistory(monthNumber, selectedYear);
        
        // Update available years and months from API
        if (response.availableYears && response.availableMonths) {
          setAvailableYears(response.availableYears);
          setAvailableMonthsByYear(response.availableMonths);
        }
        
        // Map API data to our component's data format
        const formattedQuestions: QuestionData[] = response.history.map((entry: HistoryEntry, index: number) => {
          const entryDate = new Date(entry.date);
          // Determine question status
          let status: QuestionStatus = 'both_answered_visible';
          
          if (entry.userAnswer && !entry.partnerAnswer) {
            status = 'user_answered_waiting';
          } else if (!entry.userAnswer && entry.partnerAnswer) {
            status = 'partner_answered_waiting';
          } else if (entry.userAnswer && entry.partnerAnswer && !entry.bothAnswered) {
            status = 'both_answered_hidden';
          }
          
          return {
            id: index + 1, // Fallback ID if the API doesn't provide one
            date: entry.date,
            day: entryDate.getDate(),
            question: entry.question.text,
            // Determine time of day (this is a guess - adjust if you have actual data)
            timeOfDay: entryDate.getHours() < 12 ? 'morning' : 'evening',
            status,
            userAnswer: entry.userAnswer,
            partnerAnswer: entry.partnerAnswer
          };
        });
        
        // Sort by date, most recent first
        const sortedQuestions = formattedQuestions.sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        
        // Update year-month data structure
        setYearMonthData(prev => {
          const yearStr = String(selectedYear);
          const newData = { ...prev };
          
          if (!newData[yearStr]) {
            newData[yearStr] = {};
          }
          
          newData[yearStr][selectedMonth] = sortedQuestions;
          return newData;
        });
        
        setQuestions(sortedQuestions);
        setError(null);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (err: any) {
        console.error('Error fetching question history:', err);
        setError(err.response?.data?.error || 'Failed to load question history');
        setQuestions([]);
      } finally {
        setLoading(false);
      }
    };

    // Make an initial API call to get all available years and months
    const fetchInitialData = async () => {
      if (availableYears.length === 0) {
        try {
          setLoading(true);
          // Just get the current month data but use it to initialize available years/months
          const response = await getAnswerHistory(currentMonth, currentYear);
          
          if (response.availableYears && response.availableMonths) {
            setAvailableYears(response.availableYears);
            setAvailableMonthsByYear(response.availableMonths);
            
            // Update selected month/year if needed
            if (response.availableYears.length > 0 && !response.availableYears.includes(selectedYear)) {
              const latestYear = Math.max(...response.availableYears);
              setSelectedYear(latestYear);
              
              if (response.availableMonths[latestYear] && response.availableMonths[latestYear].length > 0) {
                const latestMonth = Math.max(...response.availableMonths[latestYear]);
                setSelectedMonth(new Date(0, latestMonth - 1).toLocaleString('default', { month: 'long' }));
              }
            }
          }
          
          if (selectedYear && selectedMonth) {
            fetchQuestionHistory();
          }
        } catch (error) {
          console.error("Error fetching initial data:", error);
          setLoading(false);
        }
      } else {
        fetchQuestionHistory();
      }
    };

    fetchInitialData();
    
  }, [selectedYear, selectedMonth, currentMonth, currentYear, availableYears.length]);

  interface StatusInfo {
    icon: React.ReactNode;
    text: string;
    color: string;
  }

  const getStatusInfo = (status: QuestionStatus): StatusInfo => {
    switch (status) {
      case 'user_answered_waiting':
        return { 
          icon: <HeartIcon className="w-5 h-5 opacity-60" />, 
          text: 'Waiting for partner...', 
          color: 'text-blue-400' 
        };
      case 'partner_answered_waiting':
        return { 
          icon: <HeartIcon className="w-5 h-5 opacity-60" />, 
          text: "You haven't answered yet", 
          color: 'text-amber-400' 
        };
      case 'both_answered_hidden':
        return { 
          icon: <HeartIcon className="w-5 h-5 opacity-80" />, 
          text: 'Answers hidden until you respond', 
          color: 'text-gray-400' 
        };
      case 'both_answered_visible':
        return { 
          icon: <HeartSolidIcon className="w-5 h-5 text-red-400" />, 
          text: 'added to Cluster!', 
          color: 'text-red-400' 
        };
      default:
        return { icon: null, text: '', color: '' };
    }
  };

  const navigateMonth = (direction: 'prev' | 'next'): void => {
    const currentMonthIndex = months.indexOf(selectedMonth);
    if (direction === 'prev') {
      if (currentMonthIndex > 0) {
        setSelectedMonth(months[currentMonthIndex - 1]);
      } else {
        const prevYear = selectedYear - 1;
        const prevYearIndex = years.indexOf(prevYear);
        if (prevYearIndex !== -1) {
          const prevYearMonths = getAvailableMonths(prevYear);
          setSelectedYear(prevYear);
          setSelectedMonth(prevYearMonths[prevYearMonths.length - 1]);
        }
      }
    } else {
      if (currentMonthIndex < months.length - 1) {
        setSelectedMonth(months[currentMonthIndex + 1]);
      } else {
        const nextYear = selectedYear + 1;
        const nextYearIndex = years.indexOf(nextYear);
        if (nextYearIndex !== -1) {
          const nextYearMonths = getAvailableMonths(nextYear);
          setSelectedYear(nextYear);
          setSelectedMonth(nextYearMonths[0]);
        }
      }
    }
  };

  const QuestionCard: React.FC<{ question: QuestionData; index: number }> = ({ question, index }) => {
    const statusInfo = getStatusInfo(question.status);
    const canViewAnswers = question.status === 'both_answered_visible';
    const isExpanded = expandedCard === question.id;

    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.01 }}
        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50 mb-6"
      >
        {/* Date and Time */}
        <div className="flex items-center gap-4 mb-4">
          <div className="text-4xl font-light text-gray-800 dark:text-gray-200">
            {question.day}
          </div>
          <div className="flex flex-col">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {new Date(question.date).toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
            <div className="flex items-center gap-1 mt-1">
              {question.timeOfDay === 'morning' ? (
                <SunIcon className="w-4 h-4 text-amber-400" />
              ) : (
                <MoonIcon className="w-4 h-4 text-indigo-400" />
              )}
              <span className="text-xs text-gray-500 dark:text-gray-500 capitalize">
                {question.timeOfDay}
              </span>
            </div>
          </div>
        </div>

        {/* Question */}
        <div className="mb-4">
          <p className="text-lg text-gray-800 dark:text-gray-200 leading-relaxed">
            {question.question}
          </p>
        </div>

        {/* Status */}
        <div className="flex items-center justify-between">
          <div className={`flex items-center gap-2 ${statusInfo.color}`}>
            {statusInfo.icon}
            <span className="text-sm">{statusInfo.text}</span>
          </div>

          {canViewAnswers && (
            <button
              onClick={() => {
                if (isMobile) {
                  setSelectedAnswerOverlay(question);
                } else {
                  setExpandedCard(isExpanded ? null : question.id);
                }
              }}
              className="flex items-center gap-1 text-sm text-red-400 hover:text-red-300 transition-colors"
            >
              View Answers
              {!isMobile && (
                <ChevronDownIcon 
                  className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                />
              )}
            </button>
          )}
        </div>

        {/* Desktop Expanded Answers */}
        {!isMobile && (
          <AnimatePresence>
            {isExpanded && canViewAnswers && (
              <motion.div
                layout
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"
              >
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                      Your Answer
                    </div>
                    <p className="text-gray-700 dark:text-gray-300">
                      {question.userAnswer}
                    </p>
                  </div>
                  <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-2">
                      Partner&apos;s Answer
                    </div>
                    <p className="text-gray-700 dark:text-gray-300">
                      {question.partnerAnswer}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </motion.div>
    );
  };

  const MobileNavigation: React.FC = () => (
    <div className="sticky top-0 z-10 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between">
        <button
          onClick={() => navigateMonth('prev')}
          className="p-2 rounded-full z-20 bg-gray-100 dark:bg-gray-800 transition-colors relative left-20"
        >
          <ChevronLeftIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
        </button>
        
        <div className="text-center">
          <div className="text-2xl font-light text-gray-800 dark:text-gray-200">
            {selectedYear}
          </div>
          <div className="text-lg text-gray-600 dark:text-gray-400">
            {selectedMonth}
          </div>
        </div>

        <button
          onClick={() => navigateMonth('next')}
          className="p-2 rounded-full z-20 bg-gray-100 dark:bg-gray-800 transition-colors relative right-20"
        >
          <ChevronRightIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
        </button>
      </div>
    </div>
  );

  const DesktopSidebar: React.FC = () => (
    <motion.div 
      className={`fixed left-0 top-0 h-full ${sidebarCollapsed ? 'w-16' : 'w-64'} bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-r border-gray-200 dark:border-gray-700 overflow-y-auto transition-all duration-300 ease-in-out`}
      layout
    >
      <div className="p-6 relative">
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.h2 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-6"
            >
              Question History
            </motion.h2>
          )}
        </AnimatePresence>
        
        {/* Collapse/Expand button */}
        <button 
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="absolute top-6 right-4 p-1.5 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
          aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {sidebarCollapsed ? (
            <ChevronDoubleRightIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          ) : (
            <ChevronDoubleLeftIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          )}
        </button>
        
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div 
              layout
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              {years.map(year => (
                <div key={year} className="space-y-2">
                  <button
                    onClick={() => setSelectedYear(year)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      selectedYear === year
                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    {year}
                  </button>
                  
                  {selectedYear === year && (
                    <div className="ml-4 space-y-1">
                      {getAvailableMonths(year).map(month => {
                        const count = yearMonthData[String(year)]?.[month]?.length || 0;
                        return (
                          <button
                            key={month}
                            onClick={() => setSelectedMonth(month)}
                            className={`w-full text-left px-3 py-1 rounded text-sm transition-colors ${
                              selectedMonth === month
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400'
                                : 'hover:bg-gray-50 dark:hover:bg-gray-800/50 text-gray-500 dark:text-gray-500'
                            }`}
                          >
                            {month} {count > 0 && `(${count})`}
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Compact sidebar content when collapsed */}
        {sidebarCollapsed && (
          <div className="mt-8 flex flex-col items-center space-y-4">
            {years.map(year => (
              <button
                key={year}
                onClick={() => {
                  setSelectedYear(year);
                  setSidebarCollapsed(false);
                }}
                className={`w-10 h-10 flex items-center justify-center rounded-full transition-colors ${
                  selectedYear === year
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                }`}
                title={`${year}`}
              >
                <span className="text-xs font-medium">{year}</span>
              </button>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );

  const AnswerOverlay: React.FC = () => (
    <AnimatePresence>
      {selectedAnswerOverlay && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-end md:items-center justify-center p-4"
          onClick={() => setSelectedAnswerOverlay(null)}
        >
          <motion.div
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="bg-white dark:bg-gray-800 rounded-t-2xl md:rounded-2xl w-full max-w-2xl max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="sticky top-0 bg-white dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                Question Answers
              </h3>
              <button
                onClick={() => setSelectedAnswerOverlay(null)}
                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <XMarkIcon className="w-6 h-6 text-gray-500" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-6">
                <p className="text-lg text-gray-800 dark:text-gray-200 leading-relaxed">
                  {selectedAnswerOverlay.question}
                </p>
              </div>
              
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                    Your Answer
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">
                    {selectedAnswerOverlay.userAnswer}
                  </p>
                </div>
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-2">
                    Partner&apos;s Answer
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">
                    {selectedAnswerOverlay.partnerAnswer}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-blue-100 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900/20">
      {/* Desktop Sidebar */}
      {!isMobile && <DesktopSidebar />}
      
      {/* Mobile Navigation */}
      {isMobile && <MobileNavigation />}
      
      {/* Main Content */}
      <div className={`${!isMobile ? (sidebarCollapsed ? 'ml-16' : 'ml-64') : ''} p-6 transition-all duration-300`}>
        <div className="max-w-4xl mx-auto">
          {!isMobile && (
            <div className="mb-8">
              <h1 className="text-3xl font-light text-gray-800 dark:text-gray-200 mb-2">
                {selectedMonth} {selectedYear}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {questions.length} question{questions.length !== 1 ? 's' : ''} this month
              </p>
            </div>
          )}
          
          <div ref={contentRef}>
            {loading ? (
              <div className="flex justify-center items-center min-h-[200px]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : error ? (
              <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
                <p>{error}</p>
              </div>
            ) : questions.length > 0 ? (
              questions.map((question: QuestionData, index: number) => (
                <QuestionCard 
                  key={question.id} 
                  question={question} 
                  index={index}
                />
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">
                  No questions found for this period
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Answer Overlay for Mobile */}
      <AnswerOverlay />
    </div>
  );
};

export default QuestionHistory;
