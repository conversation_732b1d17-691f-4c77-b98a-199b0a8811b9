'use client';
import { useState, useEffect } from 'react';
import TimezoneTimePicker from './TimezoneTimePicker';
import { updateTimezonePreferences } from '@/utils/api';

type Props = {
  initialTimezone?: string;
  initialTime?: string;
  onSuccess?: () => void;
};

export default function TimezoneSettings({ 
  initialTimezone = '', 
  initialTime = '08:00',
  onSuccess = () => {} 
}: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [formData, setFormData] = useState({
    timezone: initialTimezone,
    time: initialTime
  });

  // Update local state when props change
  useEffect(() => {
    setFormData({
      timezone: initialTimezone,
      time: initialTime
    });
  }, [initialTimezone, initialTime]);

  const handleChange = (value: { timezone: string; time: string }) => {
    setFormData(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate inputs
      if (!formData.timezone || !formData.time) {
        throw new Error('Please select both timezone and preferred time');
      }

      // Submit to API
      await updateTimezonePreferences(formData.timezone, formData.time);
      
      setSuccess('Timezone preferences updated successfully!');
      onSuccess();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update timezone preferences';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
      <h2 className="text-xl font-semibold text-white mb-4">Timezone Preferences</h2>
      <p className="text-gray-300 mb-4">
        Set your preferred timezone and time for receiving daily questions
      </p>
      
      <form onSubmit={handleSubmit}>
        <TimezoneTimePicker value={formData} onChange={handleChange} />
        
        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            {success}
          </div>
        )}
        
        <div className="mt-6">
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              isLoading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {isLoading ? 'Updating...' : 'Save Preferences'}
          </button>
        </div>
      </form>
    </div>
  );
}