'use client';

import { useEffect, useState, useRef } from 'react';
import { Listbox, ListboxButton, ListboxOptions, ListboxOption, Transition } from '@headlessui/react';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/24/solid';
import timezones from './timezones';

type Props = {
  value: {
    timezone: string;
    time: string;
  };
  onChange: (value: { timezone: string; time: string }) => void;
};

export default function TimezoneTimePicker({ value, onChange }: Props) {
  // Use useRef to track if we already initialized the timezone
  const initialized = useRef(false);
  
  // Initialize with the provided value or empty strings
  const [selectedTz, setSelectedTz] = useState(value.timezone || '');
  const [time, setTime] = useState(value.time || '');
  const [autoDetected, setAutoDetected] = useState(false);

  // Handle initial timezone detection - only run once
  useEffect(() => {
    if (initialized.current) return;
    
    if (!selectedTz) {
      // Auto-detect timezone if not provided
      const detectedTz = Intl.DateTimeFormat().resolvedOptions().timeZone;
      setSelectedTz(detectedTz);
      setAutoDetected(true);
      onChange({ timezone: detectedTz, time: time || '08:00' });
    }
    
    initialized.current = true;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle timezone changes
  const handleTimezoneChange = (newTimezone: string) => {
    setSelectedTz(newTimezone);
    setAutoDetected(false);
    onChange({ timezone: newTimezone, time });
  };

  // Handle time changes
  const handleTimeChange = (newTime: string) => {
    setTime(newTime);
    onChange({ timezone: selectedTz, time: newTime });
  };

  return (
    <div className="space-y-4">
      {/* Timezone Selector */}
      <div>
        <label className="block text-sm font-medium text-white mb-1">
          Timezone
        </label>
        <Listbox value={selectedTz} onChange={handleTimezoneChange}>
          <div className="relative">
            <ListboxButton className="relative w-full cursor-default rounded-lg bg-gray border border-gray-300 py-2 pl-3 pr-10 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:text-sm">
              <span className="block truncate">{selectedTz}</span>
              <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <ChevronUpDownIcon className="h-5 w-5 text-gray-400" />
              </span>
            </ListboxButton>
            <Transition
              as="div"
              className="absolute z-10 mt-1 w-full rounded-md bg-gray-800 shadow-lg max-h-60 overflow-auto border border-gray-200"
            >
              <ListboxOptions className="py-1 text-sm">
                {timezones.map((tz) => (
                  <ListboxOption
                    key={tz}
                    value={tz}
                    className={({ active }) =>
                      `relative cursor-default select-none py-2 pl-10 pr-4 ${
                        active ? 'bg-indigo-100 text-green-900' : 'text-white'
                      }`
                    }
                  >
                    {({ selected }) => (
                      <>
                        <span
                          className={`block truncate ${
                            selected ? 'font-medium' : 'font-normal'
                          }`}
                        >
                          {tz}
                        </span>
                        {selected ? (
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-indigo-600">
                            <CheckIcon className="h-5 w-5" />
                          </span>
                        ) : null}
                      </>
                    )}
                  </ListboxOption>
                ))}
              </ListboxOptions>
            </Transition>
          </div>
        </Listbox>
        {autoDetected && (
          <p className="text-xs text-green-600 mt-1">
            Automatically detected timezone
          </p>
        )}
      </div>
      {/* Time Picker */}
      <div>
        <label className="block text-sm font-medium text-white mb-1">Time</label>
        <input
          type="time"
          value={time}
          onChange={(e) => handleTimeChange(e.target.value)}
          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        />
      </div>
    </div>
  );
}
