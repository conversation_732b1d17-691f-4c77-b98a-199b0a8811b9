// PWAInstallPrompt.tsx - Component to prompt users to install the PWA
import { useState, useEffect } from "react";
import usePWA from "@/hooks/usePWA";

interface PWAInstallPromptProps {
  className?: string;
}

export default function PWAInstallPrompt({ className = "" }: PWAInstallPromptProps) {
  const { isPWASupported, isInstalled, canInstall, promptInstall } = usePWA();
  const [dismissed, setDismissed] = useState(false);
  
  // Check if we should show the banner
  const [shouldShow, setShouldShow] = useState(false);
  
  // Retrieve the dismissed state from localStorage
  useEffect(() => {
    // Only run client-side
    if (typeof window !== "undefined") {
      const isDismissed = localStorage.getItem("pwa-prompt-dismissed") === "true";
      setDismissed(isDismissed);
      
      // Show prompt if:
      // 1. PWA is supported
      // 2. App is not already installed
      // 3. User can install (prompt is available)
      // 4. User hasn't dismissed the prompt
      setShouldShow(isPWASupported && !isInstalled && canInstall && !isDismissed);
    }
  }, [isPWASupported, isInstalled, canInstall]);
  
  // Handle dismiss
  const handleDismiss = () => {
    setDismissed(true);
    localStorage.setItem("pwa-prompt-dismissed", "true");
    setShouldShow(false);
  };
  
  // Handle install button click
  const handleInstall = async () => {
    await promptInstall();
    setShouldShow(false);
  };
  
  // Don't render if we shouldn't show
  if (!shouldShow) {
    return null;
  }
  
  return (
    <div className={`fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-50 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Install Novan App
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
            Add Novan to your home screen for quick access to your connections.
          </p>
          <div className="flex mt-3 space-x-2">
            <button
              onClick={handleInstall}
              className="flex-1 px-3 py-2 text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Install
            </button>
            <button
              onClick={handleDismiss}
              className="px-3 py-2 text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Maybe Later
            </button>
          </div>
        </div>
        <button
          onClick={handleDismiss}
          className="ml-4 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
        >
          <span className="sr-only">Close</span>
          <svg
            className="w-5 h-5"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
  );
}
