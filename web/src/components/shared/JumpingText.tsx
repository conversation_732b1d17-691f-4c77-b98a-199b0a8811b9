'use client';

import React, { useRef } from 'react';
import { gsap } from 'gsap';
import { useGSAP } from '@gsap/react';

interface JumpingTextProps {
  text: string;
  className?: string;
}

const JumpingText: React.FC<JumpingTextProps> = ({ text, className = '' }) => {
  const containerRef = useRef<HTMLSpanElement>(null);

  useGSAP(() => {
    const letters = containerRef.current?.querySelectorAll<HTMLSpanElement>('.letter');
    if (!letters) return;

    gsap.set(letters, { display: 'inline-block' });

    gsap.to(letters, {
      y: -10,
      repeat: -1,
      yoyo: true,
      stagger: {
        each: 0.1,
        repeat: -1,
        yoyo: true,
      },
      ease: 'power2.inOut',
      duration: 1,
    });
  }, { scope: containerRef });

  return (
    <span ref={containerRef} className={className}>
      {text.split('').map((char, index) => (
        <span key={index} className="letter">
          {char}
        </span>
      ))}
    </span>
  );
};

export default JumpingText;
