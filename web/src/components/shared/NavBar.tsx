'use client';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { logout as logoutAction } from '@/store/features/auth/authSlice';
import { logout as logoutApi } from '@/utils/api';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import ThemeToggle from './ThemeToggle';

export default function NavBar() {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const pathname = usePathname();
  const router = useRouter();

  // Handle logout
  const handleLogout = () => {
    logoutApi(); // Clear token in cookies
    dispatch(logoutAction()); // Clear auth state in Redux
    router.push('/login');
  };

  // Define navigation links
  const navLinks = user?.partner ? [
    { href: '/dashboard', label: 'Dashboard' },
    { href: '/dashboard/history', label: 'History' },
    { href: '/dashboard/settings', label: 'Settings' },
    { href: '/profile', label: 'Profile' },
  ] : [
    { href: '/dashboard', label: 'Dashboard' },
    { href: '/dashboard/relationship', label: 'Relationship' },
    { href: '/profile', label: 'Profile' },
  ];

  const isActive = (path: string) => {
    if (path === '/dashboard') {
      return pathname === '/dashboard' || pathname === '/';
    }
    return pathname.startsWith(path);
  };

  return (
    <nav className="bg-white dark:bg-gray-800 shadow-md">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link 
              href="/"
              className="flex-shrink-0 flex items-center"
            >
              <span className="text-xl font-bold text-blue-600">Novan</span>
            </Link>
          </div>
          {isAuthenticated && user && (
            <div className="flex items-center">
              <div className="hidden md:ml-6 md:flex md:space-x-4">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      isActive(link.href)
                        ? 'bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                        : 'text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'
                    }`}
                  >
                    {link.label}
                  </Link>
                ))}
              </div>
              <div className="ml-4 flex items-center md:ml-6 space-x-2">
                <ThemeToggle variant="icon" className="hover:bg-gray-50 dark:hover:bg-gray-700" />
                <div className="ml-3 relative">
                  <button
                    onClick={handleLogout}
                    className="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          )}
          {!isAuthenticated && (
            <div className="flex items-center">
              <ThemeToggle variant="icon" className="hover:bg-gray-50 dark:hover:bg-gray-700" />
            </div>
          )}
        </div>
      </div>
      {/* Mobile navigation */}
      {isAuthenticated && user && (
        <div className="md:hidden border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between px-2 py-3 space-x-1">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`flex-1 px-3 py-2 rounded-md text-sm font-medium text-center ${
                  isActive(link.href)
                    ? 'bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : 'text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'
                }`}
              >
                {link.label}
              </Link>
            ))}
            <div className="flex items-center justify-center px-3">
              <ThemeToggle variant="icon" className="hover:bg-gray-50 dark:hover:bg-gray-700" />
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}