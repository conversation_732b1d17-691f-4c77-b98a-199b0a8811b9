"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  HomeIcon,
  BriefcaseIcon,
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  ClipboardDocumentListIcon,
  Cog6ToothIcon,
} from "@heroicons/react/24/outline";
import { selectIsMobile } from "@/store/features/device/deviceSlice";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { logout as logoutAction } from "@/store/features/auth/authSlice";
import { logout as logoutApi } from "@/utils/api";
import { usePathname, useRouter } from "next/navigation";
import { HeartIcon } from "@heroicons/react/24/solid";

// Define the type for a navigation item
interface NavItem {
  name: string;
  href?: string;
  icon: typeof HomeIcon;
  color: string;
}

const Navbar = () => {
  const [activeTab, setActiveTab] = useState<string>("Dashboard");
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const isMobile = useAppSelector(selectIsMobile);
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const pathname = usePathname();
  const router = useRouter();

  // Handle logout
  const handleLogout = () => {
    logoutApi(); // Clear token in cookies
    dispatch(logoutAction()); // Clear auth state in Redux
    router.push("/login");
  };

  // Define authenticated navigation items based on user partner status
  const authenticatedNavItems: NavItem[] = user?.partner
    ? [
        {
          name: "Dashboard",
          href: "/dashboard",
          icon: HomeIcon,
          color: "text-blue-400!",
        },
        {
          name: "History",
          href: "/dashboard/history",
          icon: ClipboardDocumentListIcon,
          color: "text-purple-400!",
        },
        {
          name: "Settings",
          href: "/dashboard/settings",
          icon: Cog6ToothIcon,
          color: "text-green-400!",
        },
        {
          name: "Profile",
          href: "/profile",
          icon: UserIcon,
          color: "text-pink-400!",
        },
        {
          name: "Cluster",
          href: "/dashboard/3d",
          icon: HeartIcon,
          color: "text-pink-400!",
        },
      ]
    : [
        {
          name: "Dashboard",
          href: "/dashboard",
          icon: HomeIcon,
          color: "text-blue-400!",
        },
        {
          name: "Relationship",
          href: "/dashboard/relationship",
          icon: BriefcaseIcon,
          color: "text-purple-400!",
        },
        {
          name: "Profile",
          href: "/profile",
          icon: UserIcon,
          color: "text-pink-400!",
        },
      ];

  // Define which items should be in the primary nav and which in the dropdown
  const primaryItemNames: string[] = ["Dashboard", "History", "Cluster", "Relationship"];

  // Filter items for secondary menu
  const secondaryItems: NavItem[] = authenticatedNavItems.filter(
    (item) => !primaryItemNames.includes(item.name)
  );

  const springTransition = {
    type: "spring",
    stiffness: 400,
    damping: 30,
  };

  // Define props for NavButton
  interface NavButtonProps {
    item: NavItem;
    isActive: boolean;
    onClick: () => void;
    className?: string;
  }

  // Check if a path is active
  const isActive = (path?: string) => {
    if (!path) return false;
    if (path === "/dashboard") {
      return pathname === "/dashboard" || pathname === "/";
    }
    return pathname.startsWith(path);
  };

  const NavButton: React.FC<NavButtonProps> = ({
    item,
    isActive,
    onClick,
    className = "",
  }) => {
    const Icon = item.icon;

    return (
      <motion.button
        onClick={onClick}
        className={`relative px-2 sm:px-3 py-2 sm:py-3 rounded-xl sm:rounded-2xl transition-all duration-300 group ${className} cursor-pointer`}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={springTransition}>
        <motion.div
          className={`${
            isActive ? item.color : "text-[#94a3b8]"
          } flex items-center gap-1 sm:gap-2`}
          initial={false}
          animate={{
            color: isActive ? "#ffffff" : "#94a3b8",
          }}
          transition={springTransition}>
          <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="text-xs sm:text-sm font-medium">{item.name}</span>
        </motion.div>

        {/* Active indicator */}
        <AnimatePresence>
          {isActive && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-2xl border border-white/20"
              layoutId="activeTab"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={springTransition}
            />
          )}
        </AnimatePresence>

        {/* Hover effect */}
        <motion.div
          className="absolute inset-0 bg-white/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={false}
        />
      </motion.button>
    );
  };

  const HamburgerMenu: React.FC = () => (
    <motion.div className="relative" initial={false}>
      <motion.button
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        className="relative px-2 sm:px-3 py-2 rounded-xl sm:rounded-2xl transition-all duration-300 group"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={springTransition}>
        <motion.div
          animate={{ rotate: isMenuOpen ? 180 : 0 }}
          transition={springTransition}>
          {isMenuOpen ? (
            <XMarkIcon className="w-4 h-4 sm:w-5 sm:h-5 text-slate-400" />
          ) : (
            <Bars3Icon className="w-4 h-4 sm:w-5 sm:h-5 text-slate-400" />
          )}
        </motion.div>

        <motion.div
          className="absolute inset-0 bg-white/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={false}
        />
      </motion.button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="absolute bottom-full mb-2 right-0 min-w-[150px]"
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={springTransition}>
            <div className="bg-white/10 dark:bg-black/40 backdrop-blur-xl border border-black/10 dark:border-white/10 rounded-xl sm:rounded-2xl p-1.5 sm:p-2 shadow-2xl">
              {secondaryItems.map((item) => {
                const Icon = item.icon;
                return (
                  <motion.button
                    key={item.name}
                    onClick={() => {
                      setActiveTab(item.name);
                      setIsMenuOpen(false);
                      if (item.href) router.push(item.href);
                    }}
                    className="w-full flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-3 rounded-lg sm:rounded-xl text-left hover:bg-white/10 transition-all duration-200 group"
                    whileHover={{ x: 4 }}
                    transition={springTransition}>
                    <Icon className={`w-4 h-4 sm:w-5 sm:h-5 ${item.color}`} />
                    <span className="text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white">
                      {item.name}
                    </span>
                  </motion.button>
                );
              })}
              <motion.button
                onClick={handleLogout}
                className="relative px-2 sm:px-3 py-2 sm:py-3 rounded-xl sm:rounded-2xl transition-all duration-300 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={springTransition}>
                <motion.div
                  className="flex items-center gap-1 sm:gap-2 hover:text-red-500!"
                  initial={false}
                  animate={{ color: "#94a3b8" }}
                  transition={springTransition}>
                  <XMarkIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="text-xs sm:text-sm font-medium">Logout</span>
                </motion.div>
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  return (
    <motion.div
      className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        delay: 0.1,
      }}>
      <div className="relative">
        {/* Main navbar container */}
        <div className="w-[95vw] max-w-md bg-white/10 dark:bg-black/40  backdrop-blur-xl border border-black/10 dark:border-white/10 rounded-2xl sm:max-w-fit sm:rounded-3xl px-1.5 sm:px-2 py-1.5 sm:py-2 shadow-2xl">
          <div className="flex items-center gap-0.5 sm:gap-1">
            {/* Logo */}
            <motion.div
              className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl sm:rounded-2xl mr-1 sm:mr-2 cursor-pointer"
              whileHover={{ scale: 1.05, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
              transition={springTransition}
              onClick={() => router.push("/")}>
              <span className="text-white font-bold text-base sm:text-lg">
                N
              </span>
            </motion.div>

            {/* Desktop/Tablet Navigation */}
            {!isMobile && isAuthenticated && (
              <>
                {primaryItemNames.map((i) => {
                  const item = authenticatedNavItems.find(
                    (nav) => nav.name === i
                  );
                  if (!item) return null;
                  return (
                    <NavButton
                      key={item.name}
                      item={item}
                      isActive={
                        item.href
                          ? isActive(item.href)
                          : activeTab === item.name
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        setActiveTab(item.name);
                        if (item.href) router.push(item.href);
                      }}
                    />
                  );
                })}
                {secondaryItems.length > 0 && <HamburgerMenu />}
              </>
            )}

            {/* Mobile Navigation */}
            {isMobile && isAuthenticated && (
              <>
                {primaryItemNames.map((itemName: string) => {
                  const item = authenticatedNavItems.find(
                    (nav) => nav.name === itemName
                  );
                  if (!item) return null;
                  return (
                    <NavButton
                      key={item.name}
                      item={item}
                      isActive={
                        item.href
                          ? isActive(item.href)
                          : activeTab === item.name
                      }
                      onClick={() => {
                        setActiveTab(item.name);
                        if (item.href) router.push(item.href);
                      }}
                    />
                  );
                })}
                {secondaryItems.length > 0 && <HamburgerMenu />}
              </>
            )}
          </div>
        </div>

        {/* Glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-2xl sm:rounded-3xl blur-lg sm:blur-xl -z-10 opacity-50"></div>
      </div>
    </motion.div>
  );
};

export default Navbar;
