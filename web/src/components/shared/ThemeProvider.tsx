'use client';

import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectEffectiveTheme, setSystemPreference } from '@/store/features/theme/themeSlice';
import { useAppDispatch } from '@/store/hooks';

export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  const effectiveTheme = useSelector(selectEffectiveTheme);
  const dispatch = useAppDispatch();

  // Effect to detect and update system preference
  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== 'undefined') {
      // Set up media query to detect system preference
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      // Handler to update Redux when system preference changes
      const handleChange = (e: MediaQueryListEvent) => {
        dispatch(setSystemPreference(e.matches ? 'dark' : 'light'));
      };

      // Initial dispatch
      dispatch(setSystemPreference(mediaQuery.matches ? 'dark' : 'light'));

      // Listen for changes
      mediaQuery.addEventListener('change', handleChange);

      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [dispatch]);

  // Effect to apply theme to document
  useEffect(() => {
    if (effectiveTheme === 'dark') {
      document.documentElement.classList.add('dark');
      document.documentElement.style.colorScheme = 'dark';
    } else {
      document.documentElement.classList.remove('dark');
      document.documentElement.style.colorScheme = 'light';
    }
  }, [effectiveTheme]);

  return <>{children}</>;
}