"use client";

import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setThemeMode } from "@/store/features/theme/themeSlice";
import { useState, useEffect } from "react";
import type { RootState } from "@/store";
import BulbThemeToggle from "./BulbThemeToggle";
import { SunIcon, MoonIcon, SystemIcon } from "./icons/ThemeIcons";

type ThemeToggleProps = {
  variant?: "icon" | "dropdown" | "buttons" | "bulb";
  className?: string;
};

export default function ThemeToggle({
  variant = "icon",
  className = "",
}: ThemeToggleProps) {
  const dispatch = useAppDispatch();
  const themeMode = useAppSelector((state: RootState) => state.theme.mode);
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch by only showing the toggle after component mounts
  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    const modes = ["light", "dark", "system"] as const;
    const idx = modes.indexOf(themeMode);
    const next = (idx + 1) % modes.length;
    dispatch(setThemeMode(modes[next]));
  };

  if (!mounted) {
    return <div className={`w-9 h-9 ${className}`} />;
  }

  // Icon-only circular toggle that cycles through options
  if (variant === "icon") {
    return (
      <button
        onClick={toggleTheme}
        className={`rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-[var(--color-ring)] bg-[var(--color-card)] text-[var(--color-foreground)] hover:bg-[var(--color-card-border)] transition-colors ${className}`}
        aria-label="Toggle theme">
        {themeMode === "light" ? (
          <SunIcon />
        ) : themeMode === "dark" ? (
          <MoonIcon />
        ) : (
          <SystemIcon />
        )}
      </button>
    );
  }

  // Dropdown select for theme
  if (variant === "dropdown") {
    return (
      <select
        value={themeMode}
        onChange={(e) =>
          dispatch(setThemeMode(e.target.value as "light" | "dark" | "system"))
        }
        className={`input bg-[var(--color-input)] border border-[var(--color-input-border)] rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[var(--color-ring)] ${className}`}>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
        <option value="system">System</option>
      </select>
    );
  }

  // Button group toggle
  if (variant === "buttons") {
    return (
      <div
        className={`flex rounded-lg overflow-hidden border border-[var(--color-card-border)] ${className}`}>
        <button
          onClick={() => dispatch(setThemeMode("light"))}
          className={`flex items-center gap-2 px-3 py-2 ${
            themeMode === "light"
              ? "bg-[var(--color-primary)] text-[var(--color-primary-foreground)]"
              : "bg-[var(--color-card)] hover:bg-[var(--color-input-border)] transition-colors"
          }`}
          aria-label="Light mode">
          <SunIcon /> Light
        </button>
        <button
          onClick={() => dispatch(setThemeMode("dark"))}
          className={`flex items-center gap-2 px-3 py-2 ${
            themeMode === "dark"
              ? "bg-[var(--color-primary)] text-[var(--color-primary-foreground)]"
              : "bg-[var(--color-card)] hover:bg-[var(--color-input-border)] transition-colors"
          }`}
          aria-label="Dark mode">
          <MoonIcon /> Dark
        </button>
        <button
          onClick={() => dispatch(setThemeMode("system"))}
          className={`flex items-center gap-2 px-3 py-2 ${
            themeMode === "system"
              ? "bg-[var(--color-primary)] text-[var(--color-primary-foreground)]"
              : "bg-[var(--color-card)] hover:bg-[var(--color-input-border)] transition-colors"
          }`}
          aria-label="System preference">
          <SystemIcon /> System
        </button>
      </div>
    );
  }

  // Bulb variant
  if (variant === "bulb") {
    return <BulbThemeToggle className={className} />;
  }

  // Default fallback
  return (
    <button
      onClick={toggleTheme}
      className={`rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-[var(--color-ring)] bg-[var(--color-card)] text-[var(--color-foreground)] hover:bg-[var(--color-card-border)] transition-colors ${className}`}
      aria-label="Toggle theme">
      {themeMode === "light" ? <SunIcon /> : themeMode === "dark" ? <MoonIcon /> : <SystemIcon />}
    </button>
  );
}
