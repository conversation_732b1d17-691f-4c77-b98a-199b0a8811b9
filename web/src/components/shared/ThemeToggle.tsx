'use client';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setThemeMode } from '@/store/features/theme/themeSlice';
import { useState, useEffect } from 'react';
import type { RootState } from '@/store';

// Icons for the theme toggle
const SunIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.364-1.591 1.591M21 12h-2.25m-.364 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
  </svg>
);

const MoonIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
    <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
  </svg>
);

const SystemIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25" />
  </svg>
);

type ThemeToggleProps = {
  variant?: 'icon' | 'dropdown' | 'buttons';
  className?: string;
};

export default function ThemeToggle({ variant = 'icon', className = '' }: ThemeToggleProps) {
  const dispatch = useAppDispatch();
  const themeMode = useAppSelector((state: RootState) => state.theme.mode);
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch by only showing the toggle after component mounts
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className={`w-9 h-9 ${className}`} />;
  }

  // Icon-only circular toggle that cycles through options
  if (variant === 'icon') {
    const handleToggle = () => {
      const modes = ['light', 'dark', 'system'] as const;
      const currentIndex = modes.indexOf(themeMode);
      const nextIndex = (currentIndex + 1) % modes.length;
      dispatch(setThemeMode(modes[nextIndex]));
    };

    return (
      <button
        onClick={handleToggle}
        className={`rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-[var(--color-ring)] bg-[var(--color-card)] text-[var(--color-foreground)] hover:bg-[var(--color-card-border)] transition-colors ${className}`}
        aria-label="Toggle theme"
      >
        {themeMode === 'light' ? <SunIcon /> : themeMode === 'dark' ? <MoonIcon /> : <SystemIcon />}
      </button>
    );
  }

  // Dropdown select for theme
  if (variant === 'dropdown') {
    return (
      <select
        value={themeMode}
        onChange={(e) => dispatch(setThemeMode(e.target.value as 'light' | 'dark' | 'system'))}
        className={`input bg-[var(--color-input)] border border-[var(--color-input-border)] rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[var(--color-ring)] ${className}`}
      >
        <option value="light">Light</option>
        <option value="dark">Dark</option>
        <option value="system">System</option>
      </select>
    );
  }

  // Button group toggle
  return (
    <div className={`flex rounded-lg overflow-hidden border border-[var(--color-card-border)] ${className}`}>
      <button
        onClick={() => dispatch(setThemeMode('light'))}
        className={`flex items-center gap-2 px-3 py-2 ${
          themeMode === 'light'
            ? 'bg-[var(--color-primary)] text-[var(--color-primary-foreground)]'
            : 'bg-[var(--color-card)] hover:bg-[var(--color-input-border)] transition-colors'
        }`}
        aria-label="Light mode"
      >
        <SunIcon /> Light
      </button>
      <button
        onClick={() => dispatch(setThemeMode('dark'))}
        className={`flex items-center gap-2 px-3 py-2 ${
          themeMode === 'dark'
            ? 'bg-[var(--color-primary)] text-[var(--color-primary-foreground)]'
            : 'bg-[var(--color-card)] hover:bg-[var(--color-input-border)] transition-colors'
        }`}
        aria-label="Dark mode"
      >
        <MoonIcon /> Dark
      </button>
      <button
        onClick={() => dispatch(setThemeMode('system'))}
        className={`flex items-center gap-2 px-3 py-2 ${
          themeMode === 'system'
            ? 'bg-[var(--color-primary)] text-[var(--color-primary-foreground)]'
            : 'bg-[var(--color-card)] hover:bg-[var(--color-input-border)] transition-colors'
        }`}
        aria-label="System preference"
      >
        <SystemIcon /> System
      </button>
    </div>
  );
}