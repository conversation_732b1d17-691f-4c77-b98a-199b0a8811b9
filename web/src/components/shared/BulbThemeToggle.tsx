"use client";

import { motion, useAnimation } from "framer-motion";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setThemeMode } from "@/store/features/theme/themeSlice";
import type { RootState } from "@/store";
import { LightBulbIcon } from "@heroicons/react/24/outline";
import { LightBulbIcon as LightBulbIconSolid } from "@heroicons/react/24/solid";

type BulbThemeToggleProps = {
  className?: string;
};

export default function BulbThemeToggle({
  className = "",
}: BulbThemeToggleProps) {
  const dispatch = useAppDispatch();
  const systemTheme = useAppSelector(
    (state: RootState) => state.theme.systemPreference
  );
  const themeMode = useAppSelector((state: RootState) => state.theme.mode);
  const isDark = themeMode === "system" ? systemTheme === "dark" : themeMode === "dark";
  const controls = useAnimation();
  
  const toggleTheme = () => {
    // First animate down with spring effect
    controls.start({
      y: 16,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }).then(() => {
      // Then animate back up after a brief pause
      controls.start({
        y: isDark ? 8 : 0,
        transition: {
          type: "spring",
          stiffness: 300,
          damping: 15
        }
      });
      
      // Toggle theme
      dispatch(setThemeMode(isDark ? "light" : "dark"));
    });
  };

  return (
    <div
      onClick={toggleTheme}
      className={`relative flex flex-col items-center cursor-pointer ${className}`}>
      {/* Two strings */}
      <div className="flex items-baseline relative">
        {/* Hanging string */}
        <motion.div 
          className="lamp-string w-[2px] h-20 bg-[var(--color-foreground)] absolute transform -translate-x-5 -top-7 relative"
          initial={{ y: isDark ? 8 : 0 }} 
          animate={controls}
        >
          <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-3 h-3 rounded-full bg-[var(--color-foreground)]" />
        </motion.div>
        <div className="w-[2px] h-24 bg-[var(--color-foreground)]" />
      </div>
      {/* Bulb */}
      <div className={`p-2 rounded-full shadow-lg rotate-180 ${isDark ? "bg-[#090254]" : "bg-[#fffd37]"} `}>
        { isDark ? (
          <LightBulbIcon className="w-5 h-5" />
        ) : (
          <LightBulbIconSolid className="w-5 h-5" />
        )}
      </div>
    </div>
  );
}
