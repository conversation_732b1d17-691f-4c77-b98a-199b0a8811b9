'use client';

import { useEffect } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { authLoading, loginSuccess, authError } from '@/store/features/auth/authSlice';
import { getUserProfile } from '@/utils/api';
import { getAuthToken } from '@/utils/cookies';

export default function AuthInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();

  useEffect(() => {
    const hydrateAuth = async () => {
      const token = getAuthToken();
      
      if (!token) {
        return; // No token found, user is not logged in
      }
      
      dispatch(authLoading());
      
      try {
        // Get user profile with the stored token
        const { user, relationship } = await getUserProfile();
        
        // If successful, update Redux state
        dispatch(loginSuccess({
          user,
          relationship,
          token
        }));
      } catch (err) {
        // If error occurs (like an invalid/expired token), handle it
        const errorMessage = err instanceof Error ? err.message : 'Authentication failed';
        dispatch(authError(errorMessage));
      }
    };

    hydrateAuth();
  }, [dispatch]);

  return <>{children}</>;
}