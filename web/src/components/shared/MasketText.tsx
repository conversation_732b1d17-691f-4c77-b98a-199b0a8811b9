'use client';

import React, { useRef, useId, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { useGSAP } from '@gsap/react';

gsap.registerPlugin(useGSAP);

interface MaskedTextProps {
  text: string;
  fontSize?: number;
  width?: number;
  height?: number;
}

const MaskedText: React.FC<MaskedTextProps> = ({
  text,
  fontSize = 100,
  width = 574.558,
  height = 120,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const waterRef = useRef<SVGRectElement>(null);
  // Generate unique IDs for SVG elements to prevent conflicts when multiple instances exist
  const uniqueId = useId();
  const textId = `text-${uniqueId}`;
  const maskId = `text-mask-${uniqueId}`;
  const waterId = `water-${uniqueId}`;

  // Responsive state variables
  const [responsiveWidth, setResponsiveWidth] = useState(width);
  const [responsiveFontSize, setResponsiveFontSize] = useState(fontSize);
  const [responsiveHeight, setResponsiveHeight] = useState(height);
  // Add isHovered state
  const [isHovered, setIsHovered] = useState(false);
  
  // Animation timeline refs
  const horizontalAnimRef = useRef<gsap.core.Tween | null>(null);
  const verticalAnimRef = useRef<gsap.core.Tween | null>(null);

  // Handle responsive calculations
  useEffect(() => {
    const handleResize = () => {
      const screenWidth = window.innerWidth;
      
      // Calculate responsive values based on screen width
      if (screenWidth < 640) { // Mobile breakpoint
        const scale = Math.min(screenWidth / 640, 1);
        setResponsiveWidth(width * scale);
        setResponsiveFontSize(fontSize * scale);
        setResponsiveHeight(height * scale);
      } else {
        setResponsiveWidth(width);
        setResponsiveFontSize(fontSize);
        setResponsiveHeight(height);
      }
    };

    // Initial calculation
    handleResize();
    
    // Add event listener for resize
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [width, fontSize, height]);

  useGSAP(
    () => {
      const fill = waterRef.current;
      if (!fill) return;

      // Kill previous animations if they exist
      if (horizontalAnimRef.current) horizontalAnimRef.current.kill();
      if (verticalAnimRef.current) verticalAnimRef.current.kill();

      if (isHovered) {
        // When hovered, keep the water fully filled
        gsap.set(fill, { 
          attr: { 
            x: 0,
            y: responsiveHeight * -0.17, 
            height: responsiveHeight * 1.17 
          } 
        });
      } else {
        // Horizontal animation
        horizontalAnimRef.current = gsap.fromTo(
          fill,
          { attr: { x: responsiveWidth * -0.7 } }, // Scale the starting position relative to width
          {
            attr: { x: 0},
            duration: 0.8,
            repeat: -1,
            ease: 'linear',
          }
        );

        // Vertical wave animation - modified to have smaller range of movement
        verticalAnimRef.current = gsap.fromTo(
          fill,
          { 
            attr: { 
              y: responsiveHeight * 0.6, // Start at 60% of height instead of full height
              height: responsiveHeight * 0.4 // Start with 40% height instead of 0
            } 
          },
          {
            attr: { 
              y: responsiveHeight * 0.4, // Go up to only 40% of height instead of -17%
              height: responsiveHeight * 0.6 // Increase to 60% height instead of 117%
            },
            duration: 2.5, // Slightly faster for more natural wave feeling
            repeat: -1,
            yoyo: true,
            ease: 'sine.inOut', // Changed to sine for smoother wave effect
          }
        );
      }
    },
    { scope: containerRef, dependencies: [responsiveWidth, responsiveHeight, isHovered] }
  );

  return (
    <div 
      ref={containerRef} 
      className="font-(family-name:--font-super-shiny) flex items-center justify-center w-full overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <svg
        className="block"
        width={responsiveWidth}
        height={responsiveHeight}
        viewBox={`0 0 ${responsiveWidth} ${responsiveHeight}`}
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="xMidYMid meet"
      >
        <defs>
          <pattern
            id={waterId}
            width=".25"
            height="1.1"
            patternContentUnits="objectBoundingBox"
          >
            <path
              fill="#e77eb9"
              d="M0.25,1H0c0,0,0-0.659,0-0.916c0.083-0.303,0.158,0.334,0.25,0C0.25,0.327,0.25,1,0.25,1z"
            />
          </pattern>

          <text 
            id={textId} 
            x={responsiveWidth / 2} 
            y={responsiveHeight * 0.83} 
            fontSize={responsiveFontSize}
            textAnchor="middle"
          >
            {text}
          </text>

          <mask id={maskId}>
            <use href={`#${textId}`} fill="#ffffff" />
          </mask>
        </defs>

        <use href={`#${textId}`} fill="#222" />
        <rect
          ref={waterRef}
          className="water-fill"
          mask={`url(#${maskId})`}
          fill={`url(#${waterId})`}
          x={isHovered ? responsiveWidth * -0.4 : responsiveWidth * -0.7}
          y={isHovered ? responsiveHeight * -0.17 : responsiveHeight * 0.6} // Updated to match animation starting point
          width={responsiveWidth * 2.8}
          height={isHovered ? responsiveHeight * 1.17 : responsiveHeight * 0.4} // Updated to match animation starting point
        />
      </svg>
    </div>
  );
};

export default MaskedText;
