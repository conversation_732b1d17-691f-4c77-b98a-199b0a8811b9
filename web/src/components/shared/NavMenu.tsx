"use client";

import React, { useRef, useState, useEffect } from "react";
import gsap from "gsap";
import { MenuIcon, CloseIcon } from "./navIcons";
import MaskedText from "./MasketText";
import Link from "next/link";
import { useAppSelector } from "@/store/hooks";

const NavMenu: React.FC = () => {
  // 1) State & refs
  const [open, setOpen] = useState(false);
  const tl = useRef<GSAPTimeline | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const { user } = useAppSelector((state) => state.auth);

  // 2) Build the timeline once
  useEffect(() => {
    tl.current = gsap
      .timeline({ paused: true })
      // fade in & blur the backdrop
      .to(menuRef.current, {
        autoAlpha: 1,
        backdropFilter: "blur(8px)",
        duration: 0.3,
        ease: "power2.out",
      })
      // slide the menu panel down
      .fromTo(
        menuRef.current,
        { y: "-100%" },
        { y: "0%", duration: 0.5, ease: "power2.out" },
        "<" // start at same time as the backdrop
      );
  }, []);

  // 3) Play or reverse the animation on toggle
  useEffect(() => {
    if (open) {
      tl.current?.play();
    } else {
      tl.current?.reverse();
    }
  }, [open]);

  // 4) Close on background click
  const handleClose = (e: React.MouseEvent) => {
    if (e.target === menuRef.current) {
      setOpen(false);
    }
  };

  return (
    <>
      {/* Menu button in top-right */}
      <button
        className="fixed top-4 right-4 z-50 p-2 rounded-md bg-white/30 hover:bg-white/50 transition"
        onClick={() => setOpen((v) => !v)}>
        {open ? (
          <CloseIcon className="w-6 h-6" />
        ) : (
          <MenuIcon className="w-6 h-6" />
        )}
      </button>

      {/* Sliding menu panel */}
      <nav
        ref={menuRef}
        className="fixed top-0 left-0 w-full h-full backdrop-blur-md bg-transparent z-45 flex flex-col items-center justify-center space-y-6"
        onClick={handleClose}>
        <Link href="/dashboard">
          <MaskedText text="Dashboard" />
        </Link>
        <Link href="/dashboard/history">
          <MaskedText text="History" />
        </Link>
        {user?.partner?.status === 'active' && (
          <Link href="/dashboard/settings">
            <MaskedText text="Settings" />
          </Link>
        )}
        <Link href="/dashboard/relationship">
          <MaskedText text="Relationship" />
        </Link>
        <Link href="/profile">
          <MaskedText text="Profile" />
        </Link>
      </nav>
    </>
  );
};

export default NavMenu;
