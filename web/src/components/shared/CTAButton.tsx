"use client";

import { motion } from "framer-motion";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useAppSelector } from "@/store/hooks";
import { selectEffectiveTheme } from "@/store/features/theme/themeSlice";
import { useRouter } from "next/navigation";
import JumpingText from "./JumpingText";

export default function CTAButton({
  text,
  href,
  className = "",
  textClassName = "",
}: {
  text: string;
  href: string;
  className?: string;
  textClassName?: string;
}) {
  const router = useRouter();
  const effectiveTheme = useAppSelector(selectEffectiveTheme);
  const isDark = effectiveTheme === "dark";
  const handleOnClick = () => {
    router.push(href);
  };

  return (
    <motion.div
      className={`${className} relative inline-block p-[3px] rounded-full bg-gradient-to-r from-purple-500 via-indigo-500 to-deep-indigo group`}
      whileHover={{ scale: 1.03 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}>
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 opacity-75 blur-sm group-hover:opacity-100 transition-opacity duration-200"></div>
      <button
        onClick={handleOnClick}
        className={`${
          !isDark ? "text-gray-900 " : "ext-white "
        } cursor-pointer bg-[var(--color-card)] relative px-10 py-4 rounded-full font-medium group-hover:bg-opacity-90 transition-all duration-200`}>
        <span className="relative z-10 flex items-center justify-center text-lg">
          <JumpingText
            text={text}
            className={` ${textClassName} font-(family-name:--font-outfit) tracking-wide text-[1.5rem]`}
          />
          <motion.svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="ml-2"
            initial={{ x: 0 }}
            animate={{ x: [0, 5, 0] }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeInOut",
            }}>
            <ArrowRightIcon className="w-4 h-4" />
          </motion.svg>
        </span>
      </button>
    </motion.div>
  );
}
