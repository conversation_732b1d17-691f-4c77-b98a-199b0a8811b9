"use client";

import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import gsap from "gsap";
import MaskedText from "../MasketText";
import Link from "next/link";
import { useAppSelector } from "@/store/hooks";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import LogoMaskedText from "../LogoMaskedText";

const NavBar3d: React.FC = () => {
  // 1) State & refs
  const [open, setOpen] = useState(false);
  const tl = useRef<GSAPTimeline | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const { user } = useAppSelector((state) => state.auth);

  // 2) Build the timeline once
  useEffect(() => {
    tl.current = gsap
      .timeline({ paused: true })
      // fade in & blur the backdrop
      .to(menuRef.current, {
        autoAlpha: 1,
        backdropFilter: "blur(8px)",
        duration: 0.3,
        ease: "power2.out",
      })
      // slide the menu panel down
      .fromTo(
        menuRef.current,
        { y: "200%" },
        { y: "0%", duration: 0.5, ease: "power2.out" },
        "<" // start at same time as the backdrop
      );
  }, []);

  // 3) Play or reverse the animation on toggle
  useEffect(() => {
    if (open) {
      tl.current?.play();
    } else {
      tl.current?.reverse();
    }
  }, [open]);

  // 4) Close on background click
  const handleClose = (e: React.MouseEvent) => {
    if (e.target === menuRef.current) {
      setOpen(false);
    }
  };

  return (
    <>
      {/* Menu button in top-right */}
      <motion.button
        onClick={() => setOpen(!open)}
        className="fixed bottom-4 left-[50%] transform -translate-x-1/2 z-50 border border-black/20 dark:border-white/20 px-2 sm:px-3 py-2 rounded-xl sm:rounded-2xl transition-all duration-300 group cursor-pointer"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}>
        <motion.div animate={{ rotate: open ? 180 : 0 }}>
          {open ? (
            <XMarkIcon className="w-4 h-4 sm:w-5 sm:h-5 text-black dark:text-white" />
          ) : (
            <Bars3Icon className="w-4 h-4 sm:w-5 sm:h-5 text-black dark:text-white" />
          )}
        </motion.div>

        <motion.div
          className="absolute inset-0 bg-red-500/70 dark:bg-[#e77eb9]/70 group-hover:bg-[#e77eb9] filter blur-lg rounded-2xl opacity-70 group-hover:opacity-100 transition-opacity duration-300"
          initial={false}
        />
      </motion.button>

      {/* Sliding menu panel */}
      <nav
        ref={menuRef}
        className="fixed top-0 left-0 w-full h-full backdrop-blur-md bg-transparent z-45 flex flex-col items-center justify-center space-y-6"
        onClick={handleClose}>
        <Link href="/">
          <LogoMaskedText text="Novan" fontSize={72} />
        </Link>
        <Link href="/dashboard">
          <MaskedText text="Dashboard" />
        </Link>
        <Link href="/dashboard/history">
          <MaskedText text="History" />
        </Link>
        {user?.partner?.status === "active" && (
          <Link href="/dashboard/settings">
            <MaskedText text="Settings" />
          </Link>
        )}
        <Link href="/profile">
          <MaskedText text="Profile" />
        </Link>
      </nav>
    </>
  );
};

export default NavBar3d;
