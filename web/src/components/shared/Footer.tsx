
export default function Footer() {

  return (
    <footer className="py-12 text-center text-gray-500 dark:text-gray-400 z-10 w-full">
      <p>&copy; {new Date().getFullYear()} Novan. All rights reserved.</p>
      <p className="mt-2 text-sm">
        <a
          href="https://github.com/MAX-786/novan"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
        >
          GitHub
        </a>
        &nbsp;|&nbsp;
        <a
          href="https://docs.novan.com/"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Docs
        </a>
      </p>
      <p className="mt-2 text-sm" >
        <a 
          href="https://github.com/MAX-786/novan/issues"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Report a Bug
        </a>
        &nbsp;|&nbsp;
        <a
          href="https://github.com/MAX-786/novan/discussions"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Feature Request
        </a>
      </p>
    </footer>
  );
}
