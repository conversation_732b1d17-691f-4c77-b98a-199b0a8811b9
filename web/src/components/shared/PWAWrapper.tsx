// PWAWrapper.tsx - Client-side wrapper for PWA components
'use client';

import { useEffect, useState } from 'react';
import PWAInstallPrompt from './PWAInstallPrompt';

export default function PWAWrapper() {
  // Only render on client-side to avoid hydration issues
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  if (!isClient) {
    return null;
  }
  
  return <PWAInstallPrompt />;
}
