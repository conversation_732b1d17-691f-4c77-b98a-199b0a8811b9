"use client";

import { useRef, useState } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import { useAppSelector } from "@/store/hooks";
import {
  ArrowRightIcon,
  ChevronDoubleDownIcon,
} from "@heroicons/react/24/outline";
import ParticlesBackground from "./ParticlesBackground";
import {
  selectIsMobile,
  selectShouldOptimizeAnimations,
} from "@/store/features/device/deviceSlice";
import LogoMaskedText from "../shared/LogoMaskedText";
import JumpingText from "../shared/JumpingText";

export default function HeroSection() {
  const { user, loading } = useAppSelector((state) => state.auth);
  const theme = useAppSelector((state) => state.theme.mode);
  const systemTheme = useAppSelector((state) => state.theme.systemPreference);
  const isDark = theme === "system" ? systemTheme === "dark" : theme === "dark";
  const isMobile = useAppSelector(selectIsMobile);
  const shouldOptimize = useAppSelector(selectShouldOptimizeAnimations);

  // Refs for animations
  const headerRef = useRef<HTMLDivElement>(null);

  // State for interactive elements
  const [isHoveringCTA, setIsHoveringCTA] = useState(false);
  const { scrollYProgress } = useScroll();

  // Calculate transforms based on scroll position
  const opacityForHeader = useTransform(scrollYProgress, [0, 0.2], [1, 0.8]);

  const scaleForHeader = useTransform(scrollYProgress, [0, 0.2], [1, 0.5]);

  const scrollIndicatorOpacity = useTransform(
    scrollYProgress,
    [0, 0.05],
    [1, 0]
  );
  const scrollPointerEvents = useTransform(scrollYProgress, (value) =>
    value <= 0.05 ? ("auto" as const) : ("none" as const)
  );

  // GSAP animations for the header section - optimized for mobile
  useGSAP(() => {
    if (headerRef.current) {
      // Clear any existing animations to prevent memory leaks
      gsap.killTweensOf(
        ".header-title, .header-subtitle, .header-description, .cta-container"
      );

      const tl = gsap.timeline();
      const mobileDurationFactor = isMobile ? 0.7 : 1; // Reduce animation durations on mobile
      const optimizeFactor = shouldOptimize ? 0.5 : 1; // Further reduce animations for low-performance devices

      // Skip staggered animations on low-performance devices
      if (shouldOptimize) {
        // Simpler animation for low-performance devices
        tl.fromTo(
          [
            ".header-title",
            ".header-subtitle",
            ".header-description",
            ".cta-container",
          ],
          { opacity: 0 },
          { opacity: 1, duration: 0.5, stagger: 0.1, ease: "power1.out" }
        );
      } else {
        // Full animations for higher-performance devices
        tl.from(".header-title", {
          y: isMobile ? -20 : -50,
          opacity: 0,
          duration: 0.8 * mobileDurationFactor * optimizeFactor,
          ease: "power2.out", // Using power2 instead of power3 for efficiency
        })
          .from(
            ".header-subtitle",
            {
              y: isMobile ? -15 : -30,
              opacity: 0,
              duration: 0.6 * mobileDurationFactor * optimizeFactor,
              ease: "power2.out",
            },
            "-=0.4"
          )
          .from(
            ".header-description",
            {
              y: isMobile ? -10 : -20,
              opacity: 0,
              duration: 0.6 * mobileDurationFactor * optimizeFactor,
              ease: "power2.out",
            },
            "-=0.3"
          )
          .from(
            ".cta-container",
            {
              y: isMobile ? 10 : 20,
              opacity: 0,
              duration: 0.6 * mobileDurationFactor * optimizeFactor,
              ease: "power2.out",
            },
            "-=0.3"
          );
      }
    }
  }, [headerRef, isMobile, shouldOptimize]);

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Particles.js Background */}
      <ParticlesBackground />

      {/* Header Section */}
      <motion.div
        ref={headerRef}
        className="relative flex flex-col items-center justify-center px-4 sm:px-6 min-h-[100vh] z-10"
        style={{
          opacity: opacityForHeader,
          scale: scaleForHeader,
          paddingTop: "10vh", // Position content slightly above center
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}>
        <div className="flex flex-col items-center text-center max-w-sm sm:max-w-xl md:max-w-3xl mx-auto">
          <motion.div
            className="relative inline-block mb-4 sm:mb-6"
            initial={{ scale: shouldOptimize ? 1 : 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              duration: 0.8,
              ease: "easeOut",
            }}>
            <LogoMaskedText
              text="Novan"
              fontSize={isMobile ? 190 : 272}
              width={isMobile ? 530 : 800}
              height={350}
            />
          </motion.div>

          <h2
            className={`header-subtitle text-lg sm:text-xl md:text-2xl font-medium mb-4 sm:mb-6 ${
              isDark ? "text-gray-200" : "text-gray-900"
            }`}
            style={{
              textShadow: isDark ? "0 1px 2px rgba(0,0,0,0.2)" : "none",
            }}>
            Daily Questions for Deeper Connections
          </h2>

          <p
            className={`header-description text-base sm:text-base md:text-lg max-w-xs sm:max-w-xl md:max-w-2xl mx-auto leading-relaxed ${
              isDark ? "text-gray-300" : "text-gray-700"
            }`}
            style={{
              textShadow: isDark ? "0 1px 1px rgba(0,0,0,0.1)" : "none",
            }}>
            Connect more deeply with your partner through thought-provoking
            questions. Discover new perspectives and strengthen your
            relationship, one question at a time.
          </p>

          <div className="cta-container mt-6 sm:mt-8 md:mt-10 flex flex-col sm:flex-row justify-center gap-4 max-w-xs sm:max-w-md mx-auto">
            {!loading && !user ? (
              <>
                <motion.a
                  href="/login"
                  className="relative overflow-hidden rounded-lg px-6 sm:px-8 py-3 sm:py-4 text-center font-medium text-white shadow-lg transition-all duration-300 w-full sm:w-auto"
                  whileHover={isMobile ? undefined : { scale: 1.03 }}
                  whileTap={{ scale: isMobile ? 0.98 : 0.97 }}
                  onHoverStart={() => !isMobile && setIsHoveringCTA(true)}
                  onHoverEnd={() => !isMobile && setIsHoveringCTA(false)}
                  style={{
                    boxShadow: isDark
                      ? "0 10px 15px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -4px rgba(0, 0, 0, 0.1)"
                      : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.05)",
                  }}>
                  <span className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 transition-all duration-300 ease-in-out"></span>
                  <span className="relative flex items-center justify-center space-x-2">
                    <JumpingText
                      text="Sign &nbsp; In"
                      className="font-(family-name:--font-cookie-monster) tracking-wide text-[1.5rem]"
                    />
                    {!isMobile && (
                      <motion.span
                        animate={{
                          x: isHoveringCTA && !shouldOptimize ? 5 : 0,
                        }}
                        transition={{
                          type: "spring",
                          stiffness: shouldOptimize ? 300 : 400,
                        }}>
                        <ArrowRightIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                      </motion.span>
                    )}
                    {isMobile && (
                      <span>
                        <ArrowRightIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                      </span>
                    )}
                  </span>
                </motion.a>

                <motion.a
                  href="/register"
                  className={`relative overflow-hidden rounded-lg px-6 sm:px-8 py-3 sm:py-4 text-center font-medium shadow-lg transition-all duration-300 w-full sm:w-auto ${
                    isDark
                      ? "bg-gray-800 text-white hover:bg-gray-700"
                      : "bg-gray-100 text-gray-900 hover:bg-gray-200"
                  }`}
                  whileHover={
                    shouldOptimize || isMobile
                      ? undefined
                      : {
                          scale: 1.03,
                          backgroundColor: isDark
                            ? "var(--card-bg)"
                            : "var(--card-bg)",
                        }
                  }
                  whileTap={
                    shouldOptimize
                      ? undefined
                      : { scale: isMobile ? 0.98 : 0.97 }
                  }
                  style={{
                    boxShadow: isDark
                      ? "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -4px rgba(0, 0, 0, 0.15)"
                      : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.05)",
                  }}>
                  <JumpingText
                    text="Register"
                    className="relative font-(family-name:--font-cookie-monster) tracking-wide text-[1.5rem]"
                  />
                </motion.a>
              </>
            ) : (
              <motion.a
                href="/dashboard"
                className="relative overflow-hidden rounded-lg px-6 sm:px-8 py-3 sm:py-4 text-center font-medium text-white shadow-lg transition-all duration-300 w-full sm:w-auto"
                whileHover={isMobile ? undefined : { scale: 1.03 }}
                whileTap={{ scale: isMobile ? 0.98 : 0.97 }}
                onHoverStart={() => !isMobile && setIsHoveringCTA(true)}
                onHoverEnd={() => !isMobile && setIsHoveringCTA(false)}
                style={{
                  boxShadow: isDark
                    ? "0 10px 15px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -4px rgba(0, 0, 0, 0.1)"
                    : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.05)",
                }}>
                <span className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 transition-all duration-300 ease-in-out"></span>
                <span className="relative flex items-center justify-center space-x-2">
                  <JumpingText
                    text="Dashboard"
                    className="font-(family-name:--font-funnel-display) tracking-wide text-[1.5rem]"
                  />
                  {!isMobile && (
                    <motion.span
                      animate={{ x: isHoveringCTA && !shouldOptimize ? 5 : 0 }}
                      transition={{
                        type: "spring",
                        stiffness: shouldOptimize ? 300 : 400,
                      }}>
                      <ArrowRightIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    </motion.span>
                  )}
                  {isMobile && (
                    <span>
                      <ArrowRightIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    </span>
                  )}
                </span>
              </motion.a>
            )}
          </div>

          {/* Only show scroll indicator on non-mobile and non-low-performance devices */}
          {
            <motion.div
              className="scroll-indicator mt-10 sm:mt-12 flex flex-col items-center"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.5 }}
              style={{
                opacity: scrollIndicatorOpacity,
                pointerEvents: scrollPointerEvents,
              }}>
              <p
                className={`text-xs sm:text-sm mb-1 sm:mb-2 ${
                  isDark ? "text-gray-400" : "text-gray-600"
                }`}>
                Scroll to explore
              </p>
              <motion.div
                animate={{
                  y: [0, 8, 0],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "loop" as const,
                  ease: "easeInOut",
                  // Reducing the animation complexity for better performance
                  restSpeed: 0.5,
                  restDelta: 0.5,
                }}>
                <ChevronDoubleDownIcon
                  className={`w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 ${
                    isDark ? "text-gray-400" : "text-gray-500"
                  }`}
                />
              </motion.div>
            </motion.div>
          }
        </div>
      </motion.div>
    </div>
  );
}
