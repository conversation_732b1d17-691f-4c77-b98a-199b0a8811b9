"use client";

import { useRef, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import {
  CalendarDaysIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  LockClosedIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useAppSelector } from "@/store/hooks";
import { selectIsMobile, selectShouldOptimizeAnimations } from "@/store/features/device/deviceSlice";
import { selectEffectiveTheme } from "@/store/features/theme/themeSlice";
import CTAButton from "../shared/CTAButton";

gsap.registerPlugin(ScrollTrigger);

interface Step {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  colorFrom: string;
  colorTo: string;
  borderColor: string;
  iconColor: string;
}

const steps: Step[] = [
  {
    id: 1,
    title: "Daily Question Delivery",
    description: "Each day at your preferred time, you and your partner receive the same thought-provoking question. Our question pool spans multiple categories, from lighthearted topics to deeper reflections.",
    icon: <CalendarDaysIcon className="w-12 h-12" />,
    colorFrom: "from-purple-600",
    colorTo: "to-indigo-600",
    borderColor: "border-purple-500",
    iconColor: "text-purple-500 dark:text-purple-400"
  },
  {
    id: 2,
    title: "Private Reflection",
    description: "Take time to reflect and answer independently. Your responses remain private until both partners have submitted their answers, encouraging genuine, uninfluenced perspectives.",
    icon: <LockClosedIcon className="w-12 h-12" />,
    colorFrom: "from-rose-500",
    colorTo: "to-pink-600",
    borderColor: "border-rose-500",
    iconColor: "text-rose-500 dark:text-rose-400"
  },
  {
    id: 3,
    title: "Reveal and Discuss",
    description: "Once both answers are submitted, they're revealed simultaneously. This creates an opportunity for meaningful conversation and deeper understanding of each other's thoughts and feelings.",
    icon: <ChatBubbleLeftRightIcon className="w-12 h-12" />,
    colorFrom: "from-teal-500",
    colorTo: "to-cyan-600",
    borderColor: "border-teal-500",
    iconColor: "text-teal-500 dark:text-teal-400"
  },
  {
    id: 4,
    title: "Build Your Connection History",
    description: "All your shared questions and answers are stored in your connection history. Revisit past conversations and witness how your relationship evolves and deepens over time.",
    icon: <UserGroupIcon className="w-12 h-12" />,
    colorFrom: "from-amber-500",
    colorTo: "to-orange-600",
    borderColor: "border-amber-500",
    iconColor: "text-amber-500 dark:text-amber-400"
  },
  {
    id: 5,
    title: "Grow Your Heart Cluster",
    description: "Watch your 3D heart cluster grow with each day you connect, creating a beautiful visual of your relationship journey.",
    icon: <HeartIcon className="w-12 h-12" />,
    colorFrom: "from-pink-500",
    colorTo: "to-red-600",
    borderColor: "border-pink-500",
    iconColor: "text-pink-500 dark:text-pink-400"
  },
];

export default function HowItWorksSection() {
    const { isAuthenticated, loading } = useAppSelector((state) => state.auth);
  const sectionRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const stepRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  const isMobile = useAppSelector(selectIsMobile);
  const shouldOptimize = useAppSelector(selectShouldOptimizeAnimations);
  const effectiveTheme = useAppSelector(selectEffectiveTheme);
  const isDark = effectiveTheme === 'dark';

  // Scroll-based animations
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  const headerOpacity = useTransform(scrollYProgress, [0, 0.1], [0, 1]);
  const headerY = useTransform(scrollYProgress, [0, 0.1], [50, 0]);

  // GSAP animations for the timeline and steps
  useEffect(() => {
    if (shouldOptimize || !sectionRef.current) return;

    const timeline = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 70%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Animate the vertical timeline line
    if (timelineRef.current && !isMobile) {
      timeline.fromTo(
        timelineRef.current,
        { height: 0 },
        { height: "100%", duration: 1.5, ease: "power2.inOut" }
      );
    }

    // Animate each step
    stepRefs.current.forEach((step, index) => {
      if (!step) return;
      
      timeline.fromTo(
        step,
        { 
          opacity: 0,
          y: 30 
        },
        { 
          opacity: 1,
          y: 0,
          duration: 0.6,
          delay: index * 0.15,
          ease: "power2.out"
        },
        index === 0 ? "<=0.3" : "<0.1"
      );
    });

    return () => {
      if (timeline.scrollTrigger) {
        timeline.scrollTrigger.kill();
      }
    };
  }, [sectionRef, timelineRef, stepRefs, isMobile, shouldOptimize]);

  return (
    <section ref={sectionRef} className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background image with texture effect */}
      <motion.div 
        className={`absolute inset-0 bg-cover bg-center bg-no-repeat ${isDark ? "opacity-30" : "opacity-20"} blur-sm mix-blend-soft-light`}
        style={{ backgroundImage: `url('/images/how_novan_works_bg.png')` }}
        initial={{ scale: 1 }}
        animate={{ 
          scale: [1, 1.02, 1],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      ></motion.div>

      {/* Background gradient elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-purple-400/20 to-indigo-500/20 blur-3xl rounded-full"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-rose-400/20 to-amber-500/20 blur-3xl rounded-full"></div>
      <div className="absolute bottom-1/4 right-1/4 w-1/4 h-1/4 bg-gradient-to-bl from-pink-400/10 to-rose-500/10 blur-2xl rounded-full"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section header with animation */}
        <motion.div 
          className="text-center mb-16 md:mb-24"
          style={{ opacity: headerOpacity, y: headerY }}
        >
          <span className="inline-block px-4 py-1.5 rounded-full text-sm font-medium bg-gradient-to-r from-purple-500/10 to-indigo-500/10 text-[var(--how-it-works-text-primary)] mb-4">
            Five Simple Steps
          </span>
          <h2 className="text-3xl md:text-5xl font-bold text-[var(--how-it-works-text)] mb-6 font-heading">
            How Novan Works
          </h2>
          <p className="text-xl text-[var(--how-it-works-text-secondary)] max-w-3xl mx-auto">
            Our thoughtfully designed process helps couples build deeper connections through meaningful daily interactions
          </p>
        </motion.div>

        {/* Process timeline with steps */}
        <div className="relative">
          {/* Timeline connector line with animation */}
          <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 hidden md:block">
            {/* Glowing effect container */}
            <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-3 blur-md bg-gradient-to-b from-purple-500/40 via-indigo-500/40 to-pink-500/40"></div>
            
            {/* Main line */}
            <div 
              ref={timelineRef}
              className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-500 via-indigo-500 to-pink-500 rounded-full"
            ></div>
          </div>

          {/* Steps */}
          <div className="space-y-20 md:space-y-32 relative">
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                ref={el => { stepRefs.current[index] = el; }}
                className={`relative grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-16 items-center`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ amount: 0.3 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {/* Content side */}
                <div className={`${index % 2 === 0 ? 'md:text-right order-2 md:order-1' : 'md:text-left order-2'}`}>
                  <div className="relative">
                    {/* Step number with glow effect */}
                    <div className="relative inline-block mb-4">
                      <div className={`absolute inset-0 rounded-full bg-gradient-to-r ${step.colorFrom} ${step.colorTo} blur-md opacity-30`}></div>
                      <span className="relative inline-flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold">
                        {step.id}
                      </span>
                    </div>
                    
                    {/* Content with glass-like effect */}
                    <div className={`${isDark && "bg-gray-800/10 border-gray-700/30"} relative p-6 rounded-2xl bg-white/5  backdrop-blur-sm border border-white/10 shadow-lg`}>
                      <h3 className="text-2xl font-bold text-[var(--how-it-works-text)] mb-4 font-heading">
                        {step.title}
                      </h3>
                      <p className="text-lg text-[var(--how-it-works-text-secondary)] leading-relaxed">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Icon side - with animated hover effect */}
                <div className={`${index % 2 === 0 ? 'order-1 md:order-2 flex justify-center md:justify-start' : 'flex justify-center md:justify-end'}`}>
                  <motion.div 
                    className="relative"
                    whileHover={{ scale: 1.05, rotate: [0, 5, 0, -5, 0] }}
                    transition={{ 
                      type: "spring", 
                      stiffness: 300,
                      rotate: { duration: 1, repeat: 0 }
                    }}
                  >
                    {/* Outer glow */}
                    <div className={`absolute -inset-8 bg-gradient-to-r ${step.colorFrom} ${step.colorTo} rounded-full opacity-20 blur-xl`}></div>
                    
                    {/* Inner glow */}
                    <div className={`absolute -inset-1 bg-gradient-to-r ${step.colorFrom} ${step.colorTo} rounded-full opacity-40 blur-md`}></div>
                    
                    {/* Icon container with glass effect */}
                    <div className={` ${isDark && "bg-gray-800/80 border-opacity-70"}
                      relative flex items-center justify-center w-32 h-32 rounded-full 
                      bg-white/80 backdrop-blur-sm shadow-xl 
                      border-2 ${step.borderColor}
                    `}>
                      {/* Moving background pattern */}
                      <div className="absolute inset-0 rounded-full overflow-hidden opacity-10">
                        <motion.div 
                          className="w-full h-full"
                          animate={{ 
                            backgroundPosition: ['0% 0%', '100% 100%'] 
                          }}
                          transition={{ 
                            duration: 20, 
                            repeat: Infinity, 
                            repeatType: "reverse" 
                          }}
                          style={{
                            backgroundImage: `radial-gradient(circle at 30% 30%, ${step.colorFrom.replace('from-', '')} 5%, transparent 20%), 
                                            radial-gradient(circle at 70% 70%, ${step.colorTo.replace('to-', '')} 5%, transparent 20%)`,
                            backgroundSize: '50px 50px'
                          }}
                        />
                      </div>
                      
                      {/* Icon */}
                      <div className={step.iconColor}>
                        {step.icon}
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Call to action at the end of the section */}
        <motion.div 
          className="mt-24 text-center relative z-10"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          {/* Decorative elements */}
          <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 w-64 h-64 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl -z-10"></div>
          
          {/* Animated gradient button */}
          {!loading && !isAuthenticated ? (
            <CTAButton text="Start &nbsp; Your &nbsp; Journey &nbsp; Together" href="/register" textClassName={`${isMobile ? "text-[1rem]" : "text-lg"}`} />
          ) : (
            <CTAButton text="Start &nbsp; Your &nbsp; Journey &nbsp; Together" href="/dashboard" textClassName={`${isMobile ? "text-[1rem]" : "text-lg"}`} />
          )}
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            Novan is & will be free and Open Source.
          </p>
        </motion.div>
      </div>
    </section>
  );
}
