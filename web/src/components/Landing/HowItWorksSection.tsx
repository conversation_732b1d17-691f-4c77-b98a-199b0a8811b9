"use client";

import { useRef, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import {
  CalendarDaysIcon,
  ChatBubbleLeftRightIcon,
  LockClosedIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useAppSelector } from "@/store/hooks";
import { selectIsMobile, selectShouldOptimizeAnimations } from "@/store/features/device/deviceSlice";

gsap.registerPlugin(ScrollTrigger);

interface Step {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  colorFrom: string;
  colorTo: string;
  borderColor: string;
  iconColor: string;
}

const steps: Step[] = [
  {
    id: 1,
    title: "Daily Question Delivery",
    description: "Each day at your preferred time, you and your partner receive the same thought-provoking question. Our question pool spans multiple categories, from lighthearted topics to deeper reflections.",
    icon: <CalendarDaysIcon className="w-12 h-12" />,
    colorFrom: "from-purple-600",
    colorTo: "to-indigo-600",
    borderColor: "border-purple-500",
    iconColor: "text-purple-500 dark:text-purple-400"
  },
  {
    id: 2,
    title: "Private Reflection",
    description: "Take time to reflect and answer independently. Your responses remain private until both partners have submitted their answers, encouraging genuine, uninfluenced perspectives.",
    icon: <LockClosedIcon className="w-12 h-12" />,
    colorFrom: "from-rose-500",
    colorTo: "to-pink-600",
    borderColor: "border-rose-500",
    iconColor: "text-rose-500 dark:text-rose-400"
  },
  {
    id: 3,
    title: "Reveal and Discuss",
    description: "Once both answers are submitted, they're revealed simultaneously. This creates an opportunity for meaningful conversation and deeper understanding of each other's thoughts and feelings.",
    icon: <ChatBubbleLeftRightIcon className="w-12 h-12" />,
    colorFrom: "from-teal-500",
    colorTo: "to-cyan-600",
    borderColor: "border-teal-500",
    iconColor: "text-teal-500 dark:text-teal-400"
  },
  {
    id: 4,
    title: "Build Your Connection History",
    description: "All your shared questions and answers are stored in your connection history. Revisit past conversations and witness how your relationship evolves and deepens over time.",
    icon: <UserGroupIcon className="w-12 h-12" />,
    colorFrom: "from-amber-500",
    colorTo: "to-orange-600",
    borderColor: "border-amber-500",
    iconColor: "text-amber-500 dark:text-amber-400"
  },
];

export default function HowItWorksSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const stepRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  const isMobile = useAppSelector(selectIsMobile);
  const shouldOptimize = useAppSelector(selectShouldOptimizeAnimations);
  
  // Scroll-based animations
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  const headerOpacity = useTransform(scrollYProgress, [0, 0.1], [0, 1]);
  const headerY = useTransform(scrollYProgress, [0, 0.1], [50, 0]);

  // GSAP animations for the timeline and steps
  useEffect(() => {
    if (shouldOptimize || !sectionRef.current) return;

    const timeline = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 70%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Animate the vertical timeline line
    if (timelineRef.current && !isMobile) {
      timeline.fromTo(
        timelineRef.current,
        { height: 0 },
        { height: "100%", duration: 1.5, ease: "power2.inOut" }
      );
    }

    // Animate each step
    stepRefs.current.forEach((step, index) => {
      if (!step) return;
      
      timeline.fromTo(
        step,
        { 
          opacity: 0,
          y: 30 
        },
        { 
          opacity: 1,
          y: 0,
          duration: 0.6,
          delay: index * 0.15,
          ease: "power2.out"
        },
        index === 0 ? "<=0.3" : "<0.1"
      );
    });

    return () => {
      if (timeline.scrollTrigger) {
        timeline.scrollTrigger.kill();
      }
    };
  }, [sectionRef, timelineRef, stepRefs, isMobile, shouldOptimize]);

  return (
    <section ref={sectionRef} className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background gradient elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-purple-400/20 to-indigo-500/20 blur-3xl rounded-full"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-rose-400/20 to-amber-500/20 blur-3xl rounded-full"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section header with animation */}
        <motion.div 
          className="text-center mb-16 md:mb-24"
          style={{ opacity: headerOpacity, y: headerY }}
        >
          <span className="inline-block px-4 py-1.5 rounded-full text-sm font-medium bg-gradient-to-r from-purple-500/10 to-indigo-500/10 text-purple-700 dark:text-purple-300 mb-4">
            Four Simple Steps
          </span>
          <h2 className="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 font-heading">
            How Novan Works
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our thoughtfully designed process helps couples build deeper connections through meaningful daily interactions
          </p>
        </motion.div>

        {/* Process timeline with steps */}
        <div className="relative">
          {/* Timeline connector line with animation */}
          <div 
            ref={timelineRef}
            className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-500 via-indigo-500 to-amber-500 hidden md:block"
          ></div>

          {/* Steps */}
          <div className="space-y-20 md:space-y-32 relative">
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                ref={el => { stepRefs.current[index] = el; }}
                className={`relative grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-16 items-center`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {/* Content side */}
                <div className={`${index % 2 === 0 ? 'md:text-right order-2 md:order-1' : 'md:text-left order-2'}`}>
                  <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold mb-4">
                    {step.id}
                  </span>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 font-heading">
                    {step.title}
                  </h3>
                  <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                    {step.description}
                  </p>
                </div>
                
                {/* Icon side - with animated hover effect */}
                <div className={`${index % 2 === 0 ? 'order-1 md:order-2 flex justify-center md:justify-start' : 'flex justify-center md:justify-end'}`}>
                  <motion.div 
                    className="relative"
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className={`absolute -inset-6 bg-gradient-to-r ${step.colorFrom} ${step.colorTo} rounded-full opacity-20 blur-xl`}></div>
                    <div className={`relative flex items-center justify-center w-32 h-32 rounded-full bg-white dark:bg-gray-800 shadow-xl border-4 ${step.borderColor} dark:border-opacity-70`}>
                      <div className={step.iconColor}>
                        {step.icon}
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Call to action at the end of the section */}
        <motion.div 
          className="mt-24 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-block p-1 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600">
            <button className="px-8 py-3 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-medium hover:bg-opacity-95 transition-all">
              Start Your Journey Together
            </button>
          </div>
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            No credit card required. Try Novan for free.
          </p>
        </motion.div>
      </div>
    </section>
  );
}
