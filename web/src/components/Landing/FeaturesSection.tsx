import React, { useRef, useLayoutEffect, useCallback } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import './FeaturesSection.css';
import Image from 'next/image';

gsap.registerPlugin(ScrollTrigger);

interface Feature {
  title: string;
  description: string;
  image: string;
}

const features: Feature[] = [
  {
    title: 'Feature One',
    description: 'Description for feature one.',
    image: 'https://placehold.co/400x300',
  },
  {
    title: 'Feature Two',
    description: 'Description for feature two.',
    image: 'https://placehold.co/400x300',
  },
  {
    title: 'Feature Three',
    description: 'Description for feature three.',
    image: 'https://placehold.co/400x300',
  },
  // Add more features as needed
];

const FeaturesSection: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const panelsRef = useRef<HTMLDivElement[]>([]);

  // Function to lock/unlock body scroll
  const lockBodyScroll = useCallback((lock: boolean) => {
    if (typeof window !== 'undefined') {
      if (lock) {
        document.body.style.overflow = 'hidden';
        document.body.style.height = '100%';
      } else {
        document.body.style.overflow = '';
        document.body.style.height = '';
      }
    }
  }, []);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      const panels = panelsRef.current;
      const totalPanels = panels.length;

      gsap.to(panels, {
        xPercent: -100 * (totalPanels - 1),
        ease: 'none',
        scrollTrigger: {
          trigger: containerRef.current,
          pin: true,
          scrub: 1,
          snap: 1 / (totalPanels - 1),
          end: () => '+=' + (containerRef.current?.offsetWidth || 0),
          onUpdate: (self) => {
            // Get the current progress (0 to 1)
            const progress = self.progress;
            const direction = self.direction;

            // More precise control: only lock when we're actively in the horizontal scroll
            // Allow scroll at the very beginning and very end to enable natural transitions
            const threshold = 0.01; // Very small threshold for smooth transitions

            if (direction === 1) {
              // Scrolling down: lock after threshold until near the end
              const shouldLockScroll = progress > threshold && progress < (1 - threshold);
              lockBodyScroll(shouldLockScroll);
            } else {
              // Scrolling up: lock from near beginning until before threshold
              const shouldLockScroll = progress > threshold && progress < (1 - threshold);
              lockBodyScroll(shouldLockScroll);
            }
          },
          onEnter: () => {
            // Don't immediately lock on enter - let onUpdate handle it
          },
          onLeave: () => {
            // When leaving the section (scrolling down past it), unlock scroll
            lockBodyScroll(false);
          },
          onEnterBack: () => {
            // Don't immediately lock on enter back - let onUpdate handle it
          },
          onLeaveBack: () => {
            // When leaving back (scrolling up past it), unlock scroll
            lockBodyScroll(false);
          },
        },
      });
    }, containerRef);

    return () => {
      ctx.revert();
      // Ensure scroll is unlocked when component unmounts
      lockBodyScroll(false);
    };
  }, [lockBodyScroll]);

  return (
    <section className="features-section" ref={containerRef}>
      <div className="panels">
        {features.map((feature, index) => (
          <div
            className="panel"
            key={index}
            ref={(el) => {
              if (el) panelsRef.current[index] = el;
            }}
          >
            <div className="panel-content">
              <Image
                src={feature.image}
                alt={feature.title}
                width={400}
                height={300}
              />
              <h2>{feature.title}</h2>
              <p>{feature.description}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FeaturesSection;
