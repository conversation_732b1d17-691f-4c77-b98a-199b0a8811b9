import React, { useRef, useLayoutEffect } from "react";
import { motion } from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import { selectIsMobile } from "@/store/features/device/deviceSlice";
import { selectEffectiveTheme } from "@/store/features/theme/themeSlice";
import { useAppSelector } from "@/store/hooks";
import {
  CalendarDaysIcon,
  ChatBubbleLeftRightIcon,
  ChevronDoubleDownIcon,
  HeartIcon,
  LockClosedIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

gsap.registerPlugin(ScrollTrigger);

interface Feature {
  id: number;
  title: string;
  description: string;
  image: string;
  icon: React.ReactNode;
  color: string;
  borderColor: string;
}
const features: Feature[] = [
  {
    id: 1,
    title: "Daily Questions",
    description:
      "New thought-provoking question delivered to both partners every day",
    icon: <CalendarDaysIcon className="w-8 h-8 text-purple-500" />,
    color: "from-purple-500 to-indigo-600",
    image: "/images/features/daily-questions.svg",
    borderColor: "border-purple-500",
  },
  {
    id: 2,
    title: "Private Sharing",
    description:
      "Answers remain hidden until both partners respond, ensuring authentic reflection",
    icon: <LockClosedIcon className="w-8 h-8 text-rose-400" />,
    color: "from-rose-400 to-pink-600",
    image: "/images/features/private-sharing.svg",
    borderColor: "border-rose-400",
  },
  {
    id: 3,
    title: "Connection History",
    description:
      "Review past questions and answers to see how your relationship evolves",
    icon: <ChatBubbleLeftRightIcon className="w-8 h-8 text-teal-400" />,
    color: "from-teal-400 to-cyan-600",
    image: "/images/features/connection-history.svg",
    borderColor: "border-teal-400",
  },
  {
    id: 4,
    title: "Partner Sync",
    description: "Seamlessly connect with your partner across all devices",
    icon: <UserGroupIcon className="w-8 h-8 text-amber-400" />,
    color: "from-amber-400 to-orange-600",
    image: "/images/features/partner-sync.svg",
    borderColor: "border-amber-400",
  },
  {
    id: 5,
    title: "Growing Heart Cluster",
    description: 
      "Watch your 3D heart cluster grow with each day you connect, creating a beautiful visual of your relationship journey",
    icon: <HeartIcon className="w-8 h-8 text-pink-500" />,
    color: "from-pink-500 to-red-600",
    image: "/images/features/heart-cluster.svg",
    borderColor: "border-pink-500",
  },
];

const FeaturesSection: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const panelsRef = useRef<HTMLDivElement[]>([]);
  const isMobile = useAppSelector(selectIsMobile);
  const theme = useAppSelector(selectEffectiveTheme);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      const panels = panelsRef.current;
      const totalPanels = panels.length;

      // Set initial positions of panels
      panels.forEach((panel, index) => {
        gsap.set(panel, {
          x: index === 0 ? "50%" : "120%", // Start with only the first card visible
          xPercent: -50, // Center horizontally
          rotationY: -5, // Slight rotation for 3D effect
          scale: index === 0 ? 1 : 0.95, // Full size for first one
          opacity: index === 0 ? 1 : 0.8, // First one fully visible
        });
      });

      // Create horizontal slide animation with scroll trigger
      gsap.timeline({
        scrollTrigger: {
          trigger: containerRef.current,
          pin: true,
          scrub: 1,
          snap: {
            snapTo: 1 / (totalPanels - 1),
            duration: { min: 0.2, max: 0.6 },
            delay: 0.1,
          },
          start: "top top",
          end: () => "+=" + window.innerHeight * totalPanels,
          onUpdate: (self) => {
            // Calculate which panel should be active based on scroll progress
            const activeIndex = Math.round(self.progress * (totalPanels - 1));

            // Update panels based on scroll position
            panels.forEach((panel, index) => {
              let targetX: string,
                targetXPercent: number,
                targetRotationY: number,
                targetScale: number,
                targetOpacity: number,
                targetZIndex: number;

              if (index < activeIndex) {
                // Panels that have been scrolled past - stack them on left
                const offset = activeIndex - index;
                targetX = "30%";
                targetXPercent = -50 - offset * 8; // Progressive horizontal offset for stacking
                targetRotationY = isMobile ? 0 : -15 - offset * 2; // Increasing rotation for depth
                targetScale = 0.9 - offset * 0.05; // Smaller as they go back
                targetOpacity = 1; // Fade slightly as they stack
                targetZIndex = 1;
              } else if (index === activeIndex) {
                // Current active panel - centered
                targetX = "50%";
                targetXPercent = -50;
                targetRotationY = 0;
                targetScale = 1;
                targetOpacity = 1;
                targetZIndex = 5;
              } else {
                // Future panels - off to the right
                targetX = "120%";
                targetXPercent = -50;
                targetRotationY = -5;
                targetScale = 0.95;
                targetOpacity = 0.8;
                targetZIndex = 1;
              }

              // Apply the transformations
              gsap.to(panel, {
                x: targetX,
                xPercent: targetXPercent,
                rotationY: targetRotationY,
                scale: targetScale,
                opacity: targetOpacity,
                zIndex: targetZIndex,
                duration: 0.5,
                ease: "power2.out",
              });
            });
          },
        },
      });
    }, containerRef);

    return () => ctx.revert();
  }, [isMobile, theme]);

  return (
    <section
      className="w-full h-screen overflow-hidden relative flex items-center"
      style={{ perspective: "1000px" }}
      ref={containerRef}>
      <div
        className="flex h-full w-full relative"
        style={{ transformStyle: "preserve-3d" }}>
        {features.map((feature, index) => (
          <div
            className="absolute flex items-center justify-center w-full h-full"
            style={{ willChange: "transform", pointerEvents: "none" }}
            key={index}
            ref={(el) => {
              if (el) panelsRef.current[index] = el;
            }}>
            <div
              className={`text-center ${
                theme === "dark"
                  ? "bg-[var(--card-bg)] text-[var(--foreground)]"
                  : "bg-white text-[var(--foreground)]"
              } rounded-xl p-8 shadow-lg w-4/5 transition-all duration-300 ${
                isMobile ? "max-w-none" : "max-w-[700px]"
              }`}
              style={{
                pointerEvents: "auto",
                transform: "translateZ(0)",
                backfaceVisibility: "hidden",
                boxShadow:
                  theme === "dark"
                    ? "0 10px 25px rgba(0, 0, 0, 0.3)"
                    : "0 10px 25px rgba(0, 0, 0, 0.1)",
              }}>
              <div
                className={`absolute top-4 right-4 text-sm font-medium rounded-full ${
                  theme === "dark"
                    ? "bg-[var(--card-bg)]/90 text-[var(--foreground-secondary)]"
                    : "bg-white/80 text-gray-500"
                }`}>
                <div className="flex justify-center md:justify-end">
                  <div className="relative animate-pulse">
                    <div className={`absolute -inset-4 bg-gradient-to-r ${feature.color} rounded-full opacity-20 blur-lg`}></div>
                    <div className={`relative flex items-center justify-center w-24 h-24 rounded-full bg-[var(--card-bg)] shadow-xl border-4 ${feature.borderColor}`}>
                      {feature.icon}
                    </div>
                  </div>
                </div>
              </div>
              <Image
                src={feature.image}
                alt={feature.title}
                width={isMobile ? 400 : 700}
                height={isMobile ? 300 : 400}
                priority={index === 0}
                className="max-w-full h-auto mb-6 rounded-lg shadow-lg"
              />
              <h2 className="text-2xl font-bold mb-2 text-[var(--primary)]">
                {feature.title}
              </h2>
              <p
                className={`text-base ${
                  theme === "dark"
                    ? "text-[var(--foreground-secondary)]"
                    : "text-gray-600"
                }`}>
                {feature.description}
              </p>
            </div>
          </div>
        ))}
      </div>
      {/* add scroll down animation */}
      <div className="absolute bottom-45 left-1/2 transform -translate-x-1/2">
        <motion.div
          animate={{
            y: [0, 8, 0],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            repeatType: "loop" as const,
            ease: "easeInOut",
          }}
        >
          <ChevronDoubleDownIcon className="w-6 h-6 text-gray-500" />
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
