import React, { useRef, useLayoutEffect } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import './FeaturesSection.css';
import Image from 'next/image';

gsap.registerPlugin(ScrollTrigger);

interface Feature {
  title: string;
  description: string;
  image: string;
}

const features: Feature[] = [
  {
    title: 'Feature One',
    description: 'Description for feature one.',
    image: 'https://placehold.co/400x300',
  },
  {
    title: 'Feature Two',
    description: 'Description for feature two.',
    image: 'https://placehold.co/400x300',
  },
  {
    title: 'Feature Three',
    description: 'Description for feature three.',
    image: 'https://placehold.co/400x300',
  },
  // Add more features as needed
];

const FeaturesSection: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const panelsRef = useRef<HTMLDivElement[]>([]);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      const panels = panelsRef.current;
      const totalPanels = panels.length;

      gsap.to(panels, {
        xPercent: -100 * (totalPanels - 1),
        ease: 'none',
        scrollTrigger: {
          trigger: containerRef.current,
          pin: true,
          scrub: 1,
          snap: 1 / (totalPanels - 1),
          end: () => '+=' + (containerRef.current?.offsetWidth || 0),
        },
      });
    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <section className="features-section" ref={containerRef}>
      <div className="panels">
        {features.map((feature, index) => (
          <div
            className="panel"
            key={index}
            ref={(el) => { 
              if (el) panelsRef.current[index] = el;
            }}
          >
            <div className="panel-content">
              <Image 
                src={feature.image} 
                alt={feature.title} 
                width={400}
                height={300}
              />
              <h2>{feature.title}</h2>
              <p>{feature.description}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FeaturesSection;
