'use client';

import { useEffect, useRef } from 'react';
// Correctly import particles.js
import 'particles.js';
import { useAppSelector } from '@/store/hooks';
import { selectEffectiveTheme } from '@/store/features/theme/themeSlice';
import { selectIsMobile, selectShouldOptimizeAnimations } from '@/store/features/device/deviceSlice';
import { getLowPerformanceConfig } from '@/utils/deviceDetection';

// The library adds particlesJS to the global window object
declare global {
  interface Window {
    particlesJS: (
      id: string, 
      options: object
    ) => void;
    pJSDom: Array<{
      pJS: {
        fn: {
          vendors: {
            destroypJS: () => void;
          };
        };
      };
    }>;
  }
}

export default function ParticlesBackground() {
  const containerRef = useRef<HTMLDivElement>(null);
  const effectiveTheme = useAppSelector(selectEffectiveTheme);
  const isDark = effectiveTheme === 'dark';
  const isMobile = useAppSelector(selectIsMobile);
  const shouldOptimize = useAppSelector(selectShouldOptimizeAnimations);

  useEffect(() => {
    // Cleanup previous instances to prevent multiple initializations
    if (typeof window !== 'undefined' && window.pJSDom) {
      window.pJSDom.forEach(dom => dom.pJS.fn.vendors.destroypJS());
      window.pJSDom = [];
    }
    
    // Initialize particles only if window and the function exist
    if (typeof window !== 'undefined' && window.particlesJS && containerRef.current) {
      // Get optimized settings if we should optimize animations
      const lowPerf = shouldOptimize ? getLowPerformanceConfig() : null;
      
      window.particlesJS('particles-js', {
        particles: {
          number: {
            value: lowPerf ? lowPerf.particles.number.value : 72,
            density: { enable: true, value_area: 800 },
          },
          color: { 
            value: isDark ? '#57ff7e' : '#4D60CE' 
          },
          shape: {
            type: 'circle',
            stroke: { width: 0, color: '#000000' },
            polygon: { nb_sides: 5 },
          },
          opacity: {
            value: lowPerf ? lowPerf.particles.opacity.value : (isDark ? 0.6 : 0.4),
            random: false,
            anim: { enable: false, speed: 1, opacity_min: 0.1, sync: false },
          },
          size: {
            value: lowPerf ? lowPerf.particles.size.value : (isDark ? 4 : 3),
            random: true,
            anim: { enable: false, speed: 21.5, size_min: 0.1, sync: false },
          },
          line_linked: {
            enable: true,
            distance: isMobile ? 100 : 150,
            color: isDark ? '#5e60ce' : '#7400B8',
            opacity: isDark ? 0.5 : 0.4, 
            width: isMobile ? 2 : isDark ? 1.2 : 1,
          },
          move: {
            enable: true,
            speed: lowPerf ? lowPerf.particles.move.speed : 2.5,
            direction: 'bottom',
            random: true,
            straight: false,
            out_mode: 'out',
            bounce: false,
          },
        },
        interactivity: {
          detect_on: isMobile ? 'canvas' : 'window', // 'canvas' is lighter than 'window'
          events: {
            onhover: { 
              enable: !isMobile || !lowPerf?.disableInteractivity, 
              mode: 'grab'
            },
            onclick: { enable: !isMobile || !lowPerf?.disableInteractivity, mode: 'push' },
            resize: true,
          },
          modes: {
            grab: { distance: isMobile ? 140 : 300, line_linked: { opacity: 1 } },
            bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 },
            repulse: { distance: 200, duration: 0.4 },
            push: { particles_nb: isMobile ? 2 : 6 },
            remove: { particles_nb: 2 },
          },
        },
        retina_detect: false, // Disable retina detection for better performance
      });
    }
  }, [isDark, isMobile, shouldOptimize]);

  return (
    <div className="fixed inset-0 -z-10 pointer-events-auto" ref={containerRef}>
      <div 
        id="particles-js" 
        className={`w-full h-full ${isDark ? 'bg-gray-900' : 'bg-[var(--background)]'}`}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'auto' // Ensure pointer events are enabled
        }}
      ></div>
    </div>
  );
}
