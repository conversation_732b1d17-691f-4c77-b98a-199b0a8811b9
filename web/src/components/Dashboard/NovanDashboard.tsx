import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getDailyQuestion, submitAnswer } from '@/utils/api';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { setDailyQuestion } from '@/store/features/questions/questionsSlice';
import { AmbientBackground } from './AmbientBackground';
import QuestionSection from './OuestionSection';

const NovanDashboard = () => {
  const { user } = useAppSelector((state) => state.auth);
  const [userAnswer, setUserAnswer] = useState('');
  const [showAnswers, setShowAnswers] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  
  // State for question data from API
  const [dashboardState, setDashboardState] = useState<{
    question: string;
    userAnswered: boolean;
    partnerAnswered: boolean;
    partnerAnswer: string | null;
  }>({
    question: "",
    userAnswered: false,
    partnerAnswered: false,
    partnerAnswer: null
  });

  const dispatch = useAppDispatch();
  const { dailyQuestion } = useAppSelector((state) => state.questions);
  
  // Fetch daily question on component mount
  useEffect(() => {
    const fetchDailyQuestion = async () => {
      try {
        setLoading(true);
        const response = await getDailyQuestion();
        const questionData = response.question;
        
        // Update Redux store
        dispatch(setDailyQuestion(questionData));
        
        setDashboardState({
          question: questionData.text,
          userAnswered: questionData.userAnswer !== null,
          partnerAnswered: questionData.partnerAnswer !== null,
          partnerAnswer: questionData.partnerAnswer
        });
        
        // If user already answered, set their answer
        if (questionData.userAnswer) {
          setUserAnswer(questionData.userAnswer);
        }
      } catch (err: unknown) {
        const errorMsg = err instanceof Error ? err.message : 'Failed to load today\'s question';
        console.error('Error fetching question:', errorMsg);
        setError(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if user has a partner
    if (user?.partner) {
      fetchDailyQuestion();
    }
  }, [user?.partner, dispatch]);

  // Update time every minute for gradient changes
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Get time-based gradient
  const getTimeBasedGradient = () => {
    const hour = currentTime.getHours();
    if (hour >= 5 && hour < 12) {
      return 'from-amber-50 via-orange-50 to-pink-50 dark:from-amber-900/20 dark:via-orange-900/20 dark:to-pink-900/20';
    } else if (hour >= 12 && hour < 17) {
      return 'from-blue-50 via-cyan-50 to-sky-50 dark:from-blue-900/20 dark:via-cyan-900/20 dark:to-sky-900/20';
    } else if (hour >= 17 && hour < 21) {
      return 'from-orange-50 via-red-50 to-purple-50 dark:from-orange-900/20 dark:via-red-900/20 dark:to-purple-900/20';
    } else {
      return 'from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-900/20 dark:via-purple-900/20 dark:to-pink-900/20';
    }
  };

  const handleSubmitAnswer = async () => {
    if (!userAnswer.trim() || submitting) return;
    
    setSubmitting(true);
    try {
      const response = await submitAnswer(userAnswer);
      
      // Update state to reflect that user has answered
      setDashboardState(prev => ({ 
        ...prev, 
        userAnswered: true,
        // If both have answered, show partner's answer
        partnerAnswer: response.bothAnswered ? response.partnerAnswer : prev.partnerAnswer,
        partnerAnswered: prev.partnerAnswered || response.bothAnswered
      }));
      
      // Update Redux store with new question state
      if (dailyQuestion) {
        dispatch(setDailyQuestion({
          ...dailyQuestion,
          userAnswer: userAnswer,
          partnerAnswer: response.bothAnswered ? response.partnerAnswer : dailyQuestion.partnerAnswer,
          bothAnswered: response.bothAnswered
        }));
      }
    } catch (err: unknown) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to submit your answer';
      console.error('Error submitting answer:', errorMsg);
      setError(errorMsg);
    } finally {
      setSubmitting(false);
    }
  };

  const bothAnswered = dashboardState.userAnswered && dashboardState.partnerAnswered;

  const formatDate = () => {
    return currentTime.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getPartnerStatus = () => {
    if (bothAnswered) return "Both Answered ✨";
    if (dashboardState.partnerAnswered) return "Partner Answered! 💫";
    return "Waiting for Partner...";
  };

  // Presence Orb Component
  const PresenceOrb = ({ isUser, hasAnswered, position } : { isUser: boolean, hasAnswered: boolean, position: string }) => (
    <motion.div
      className={`absolute ${position} top-18 -translate-y-1/2 block`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, ease: "easeOut" }}
    >
      <motion.div
        className={`relative w-16 h-16 rounded-full ${
          hasAnswered 
            ? 'bg-gradient-to-br from-purple-400/30 to-pink-400/30 shadow-lg shadow-purple-500/20' 
            : 'bg-gradient-to-br from-gray-300/20 to-gray-400/20'
        } backdrop-blur-sm border border-white/20 dark:border-gray-700/20`}
        animate={{
          scale: hasAnswered ? [1, 1.05, 1] : [1, 0.98, 1],
          opacity: hasAnswered ? [0.8, 1, 0.8] : [0.4, 0.6, 0.4],
        }}
        transition={{
          duration: hasAnswered ? 2 : 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        {hasAnswered && (
          <motion.div
            className="absolute inset-0 rounded-full"
            animate={{
              boxShadow: [
                '0 0 20px rgba(168, 85, 247, 0.4)',
                '0 0 40px rgba(168, 85, 247, 0.6)',
                '0 0 20px rgba(168, 85, 247, 0.4)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        )}
        
        {/* Particle effect for answered state */}
        {hasAnswered && (
          <div className="absolute inset-0 overflow-hidden rounded-full">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white/80 dark:bg-white/60 rounded-full"
                style={{
                  left: `${30 + Math.sin(i * 60 * Math.PI / 180) * 20}px`,
                  top: `${40 + Math.cos(i * 60 * Math.PI / 180) * 20}px`,
                }}
                animate={{
                  y: [-5, -15, -5],
                  opacity: [0, 1, 0],
                  scale: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.2,
                  repeat: Infinity,
                  ease: "easeOut"
                }}
              />
            ))}
          </div>
        )}
      </motion.div>
      
      <motion.p 
        className={`text-xs mt-2 text-center ${
          hasAnswered ? 'text-purple-600 dark:text-purple-400' : 'text-gray-400'
        }`}
        animate={{ opacity: hasAnswered ? 1 : 0.6 }}
      >
        {isUser ? 'You' : 'Partner'}
      </motion.p>
    </motion.div>
  );

  // Connection Line between orbs when both answered
  const ConnectionLine = () => (
    <motion.div
      className="absolute top-16 left-1/2 -translate-x-1/2 -translate-y-1/2 block pointer-events-none"
      initial={{ opacity: 0, scaleX: 0 }}
      animate={{ opacity: 1, scaleX: 1 }}
      transition={{ duration: 1.5, ease: "easeOut" }}
    >
      <motion.div
        className="w-60 xl:w-145 h-0.5 bg-gradient-to-r from-purple-600/50 via-pink-400/80 to-purple-600/50  dark:bg-gradient-to-r  dark:from-purple-400/50  dark:via-pink-400/80  dark:to-purple-400/50"
        animate={{
          opacity: [0.5, 1, 0.5]
        }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gray-300 dark:bg-white rounded-full shadow-lg"
        animate={{
          scale: [1, 1.2, 1],
          boxShadow: [
            '0 0 10px rgba(255, 255, 255, 0.8)',
            '0 0 20px rgba(255, 255, 255, 1)',
            '0 0 10px rgba(255, 255, 255, 0.8)'
          ]
        }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      />
    </motion.div>
  );

  // Check if component should display no partner message
  const noPartner = !user?.partner;

  // Main render function
  return (
    <div className={`min-h-screen bg-gradient-to-br ${getTimeBasedGradient()} transition-all duration-1000 relative overflow-hidden`}>
      {/* Ambient background elements */}
      <AmbientBackground />

      {/* Mobile Header */}
      <div className="lg:hidden px-6 pt-8 pb-4">
        <motion.h1 
          className="text-2xl font-light text-gray-800 dark:text-gray-200 mb-1"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {formatDate()}
        </motion.h1>
        <motion.p 
          className={`text-sm ${
            bothAnswered ? 'text-purple-600 dark:text-purple-400' : 
            dashboardState.partnerAnswered ? 'text-blue-600 dark:text-blue-400' : 
            'text-gray-500 dark:text-gray-400'
          }`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {getPartnerStatus()}
        </motion.p>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 lg:p-8">
        
        {!noPartner && (
          <>
            {/* Desktop: Presence Orbs */}
            <PresenceOrb 
              isUser={true} 
              hasAnswered={dashboardState.userAnswered} 
              position="left-8 xl:left-150" 
            />
            <PresenceOrb 
              isUser={false} 
              hasAnswered={dashboardState.partnerAnswered} 
              position="right-8 xl:right-150" 
            />
            
            {/* Connection Line (Desktop only, when both answered) */}
            {bothAnswered && <ConnectionLine />}
          </>
        )}

        {/* Main Question Card */}
        <motion.div
          className="w-full max-w-2xl mx-auto relative z-20"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
            
            {/* Desktop Date Header */}
            <div className="hidden lg:block text-center py-6 border-b border-gray-200/20 dark:border-gray-700/20">
              <motion.h1 
                className="text-3xl font-light text-gray-800 dark:text-gray-200 mb-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                {formatDate()}
              </motion.h1>
              
              {!noPartner ? (
                <motion.p 
                  className={`text-sm ${
                    bothAnswered ? 'text-purple-600 dark:text-purple-400' : 
                    dashboardState.partnerAnswered ? 'text-blue-600 dark:text-blue-400' : 
                    'text-gray-500 dark:text-gray-400'
                  }`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  {getPartnerStatus()}
                </motion.p>
              ) : (
                <motion.p 
                  className="text-sm text-yellow-600 dark:text-yellow-400"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  No Partner Connected
                </motion.p>
              )}
            </div>

            {/* Question Section */}
            <QuestionSection 
              noPartner={noPartner}
              loading={loading}
              error={error}
              dashboardState={dashboardState}
              userAnswer={userAnswer}
              setUserAnswer={setUserAnswer}
              handleSubmitAnswer={handleSubmitAnswer}
              submitting={submitting}
              bothAnswered={bothAnswered}
              showAnswers={showAnswers}
              setShowAnswers={setShowAnswers}
              dailyQuestion={dailyQuestion}
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default NovanDashboard;