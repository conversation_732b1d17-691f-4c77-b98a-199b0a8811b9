import { motion, AnimatePresence } from "framer-motion";
import { ChevronDownIcon, HeartIcon } from "@heroicons/react/24/outline";
import { DailyQuestion } from "@/types/api";

interface QuestionSectionProps {
  noPartner: boolean;
  loading: boolean;
  error: string | null;
  dashboardState: {
    question: string;
    userAnswered: boolean;
    partnerAnswered: boolean;
    partnerAnswer: string | null;
  };
  userAnswer: string;
  setUserAnswer: (answer: string) => void;
  handleSubmitAnswer: (e: React.FormEvent) => void;
  submitting: boolean;
  bothAnswered: boolean;
  showAnswers: boolean;
  setShowAnswers: (show: boolean) => void;
  dailyQuestion: DailyQuestion | null;
}

const QuestionSection = ({
  noPartner,
  loading,
  error,
  dashboardState,
  userAnswer,
  setUserAnswer,
  handleSubmitAnswer,
  submitting,
  bothAnswered,
  showAnswers,
  setShowAnswers,
  dailyQuestion,
}: QuestionSectionProps) => {
  return (
    <div className="p-8 lg:p-12">
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}>
        {noPartner ? (
          // Show that user is not connected with partner and also tell instructions in short
          <div className="p-4 bg-yellow-50/50 dark:bg-yellow-900/20 border border-yellow-200/50 dark:border-yellow-700/50 rounded-2xl mb-6">
            <div className="flex flex-col items-center justify-center py-6">
              <p className="text-yellow-700 dark:text-yellow-300">
                Connect with your partner to access today&apos;s question.{" "}
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                {" "}
                Go to the Relationship page to connect.{" "}
              </p>
            </div>
          </div>
        ) : loading ? (
          <div className="flex flex-col items-center justify-center py-6">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">
              Loading today&apos;s question...
            </p>
          </div>
        ) : error ? (
          <div className="p-4 bg-red-50/50 dark:bg-red-900/20 border border-red-200/50 dark:border-red-700/50 rounded-2xl mb-6">
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        ) : dashboardState.question ? (
          <h2 className="text-xl lg:text-2xl font-light text-gray-800 dark:text-gray-200 leading-relaxed mb-6">
            {dashboardState.question}
          </h2>
        ) : (
          <div className="p-4 bg-yellow-50/50 dark:bg-yellow-900/20 border border-yellow-200/50 dark:border-yellow-700/50 rounded-2xl mb-6">
            <p className="text-yellow-700 dark:text-yellow-300">
              No question available for today. Check back later!
            </p>
          </div>
        )}

        {!noPartner && !loading && !error && dashboardState.question && (
          <>
            {!dashboardState.userAnswered ? (
              <div className="space-y-4">
                <textarea
                  value={userAnswer}
                  onChange={(e) => setUserAnswer(e.target.value)}
                  placeholder="Share your heart..."
                  className="w-full p-4 bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl focus:ring-2 focus:ring-purple-500/50 focus:border-transparent transition-all duration-300 resize-none text-gray-700 dark:text-gray-300 placeholder-gray-400 dark:placeholder-gray-500"
                  rows={4}
                  maxLength={dailyQuestion?.maxLength || 500}
                />
                {dailyQuestion?.maxLength && (
                  <div className="text-xs text-right text-gray-500 dark:text-gray-400">
                    {userAnswer.length}/{dailyQuestion.maxLength}
                  </div>
                )}
                <motion.button
                  onClick={handleSubmitAnswer}
                  disabled={!userAnswer.trim() || submitting}
                  className="w-full lg:w-auto px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-2xl font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                  whileHover={{
                    scale: userAnswer.trim() && !submitting ? 1.02 : 1,
                  }}
                  whileTap={{
                    scale: userAnswer.trim() && !submitting ? 0.98 : 1,
                  }}>
                  {submitting ? "Sharing..." : "Share Answer"}
                </motion.button>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="p-4 bg-green-50/50 dark:bg-green-900/20 border border-green-200/50 dark:border-green-700/50 rounded-2xl">
                <p className="text-green-700 dark:text-green-300 flex items-center justify-center gap-2">
                  <HeartIcon className="w-5 h-5" />
                  Answer shared! ✨
                </p>
              </motion.div>
            )}
          </>
        )}
      </motion.div>

      {/* Answer Reveal Section */}
      <AnimatePresence>
        {!loading && !error && bothAnswered && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.5 }}
            className="border-t border-gray-200/20 dark:border-gray-700/20 pt-8 mt-8">
            <motion.button
              onClick={() => setShowAnswers(!showAnswers)}
              className="w-full flex items-center justify-center gap-2 p-4 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 hover:from-purple-200 hover:to-pink-200 dark:hover:from-purple-800/40 dark:hover:to-pink-800/40 rounded-2xl transition-all duration-300 text-purple-700 dark:text-purple-300 font-medium"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}>
              {showAnswers ? "Hide" : "Reveal"} Our Answers
              <motion.div
                animate={{ rotate: showAnswers ? 180 : 0 }}
                transition={{ duration: 0.3 }}>
                <ChevronDownIcon className="w-5 h-5" />
              </motion.div>
            </motion.button>

            <AnimatePresence>
              {showAnswers && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                  className="mt-6 space-y-6">
                  <div className="grid gap-6 lg:grid-cols-2">
                    <motion.div
                      className="p-6 bg-blue-50/50 dark:bg-blue-900/20 rounded-2xl border border-blue-200/30 dark:border-blue-700/30"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}>
                      <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-3">
                        Your Answer
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                        {userAnswer}
                      </p>
                    </motion.div>

                    <motion.div
                      className="p-6 bg-purple-50/50 dark:bg-purple-900/20 rounded-2xl border border-purple-200/30 dark:border-purple-700/30"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}>
                      <h4 className="font-medium text-purple-700 dark:text-purple-300 mb-3">
                        Partner&apos;s Answer
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                        {dashboardState.partnerAnswer}
                      </p>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}

        {/* Message when user has answered but partner has not */}
        {!loading &&
          !error &&
          dashboardState.userAnswered &&
          !dashboardState.partnerAnswered && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className="mt-8 p-4 border border-yellow-200/30 dark:border-yellow-700/30 rounded-2xl bg-yellow-50/50 dark:bg-yellow-900/20">
              <p className="text-yellow-700 dark:text-yellow-300 text-center">
                Waiting for your partner to answer. Check back later!
              </p>
            </motion.div>
          )}
      </AnimatePresence>
    </div>
  );
};

export default QuestionSection;
