import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import Stats from 'three/examples/jsm/libs/stats.module.js';

// Import our modular components
import { HeartScene } from './three/scenes/HeartScene';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setCurrentHeartNumber, setIsLetterOpen } from '@/store/features/hearts/heartsSlice';

interface HeartsClusterSceneProps {
  /** Number of torus-knot meshes to generate */
  count?: number;
}

const HeartsClusterScene: React.FC<HeartsClusterSceneProps> = ({ count = 50 }) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const questionHistory = useAppSelector((state) => state.questions.questionHistory);
  const heartNumbers = questionHistory.map((entry) => (new Date(entry.date).getDate()));

  useEffect(() => {
    if (!mountRef.current) return;

    // Get actual container dimensions for initial sizing
    const containerWidth = mountRef.current.clientWidth;
    const containerHeight = mountRef.current.clientHeight;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    // Initialize with container size rather than props
    renderer.setSize(containerWidth, containerHeight);
    // Enable tone mapping for HDR
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;
    mountRef.current.appendChild(renderer.domElement);

    // Stats panel
    const stats = new Stats();
    mountRef.current.appendChild(stats.dom);

    // Camera
    const camera = new THREE.PerspectiveCamera(70, containerWidth / containerHeight, 0.1, 50);
    camera.position.z = 30; // Position camera closer to see the objects better

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.minDistance = 5;
    controls.maxDistance = 30;

    // Initialize our scenes
    const onHeartClick = (heartNumber: number) => {
      dispatch(setCurrentHeartNumber(heartNumber));
      dispatch(setIsLetterOpen(true));
    };
    const torusScene = new HeartScene({ count, onHeartClick, heartNumbers });

    // Track if the last interaction was from touch
    let isTouch = false;

    // Variables to track mouse/touch interactions
    let interactionStartTime = 0;
    let interactionStartPosition = { x: 0, y: 0 };
    const CLICK_DURATION_THRESHOLD = 300; // milliseconds
    const CLICK_DISTANCE_THRESHOLD = 10; // pixels

    // Mouse event handling
    function handleMouseDown(event: MouseEvent) {
      if (isTouch) {
        isTouch = false;
        return;
      }

      // Record time and position when mouse is pressed
      interactionStartTime = Date.now();
      interactionStartPosition = { x: event.clientX, y: event.clientY };
    }

    function handleMouseUp(event: MouseEvent) {
      if (isTouch || interactionStartTime === 0) {
        return;
      }

      const interactionEndTime = Date.now();
      const interactionDuration = interactionEndTime - interactionStartTime;

      // Only trigger if click was quick (not a long press for rotation/scrolling)
      if (interactionDuration < CLICK_DURATION_THRESHOLD) {
        // Calculate distance moved to determine if it was a click or a drag
        const distX = Math.abs(event.clientX - interactionStartPosition.x);
        const distY = Math.abs(event.clientY - interactionStartPosition.y);

        // If movement was minimal (user didn't drag)
        if (distX < CLICK_DISTANCE_THRESHOLD && distY < CLICK_DISTANCE_THRESHOLD) {
          // Get mouse coordinates normalized to -1 to 1
          const rect = renderer.domElement.getBoundingClientRect();
          const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
          const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

          // Pass to HeartScene for raycasting and detection
          torusScene.handleMouseClick(x, y, camera);
        }
      }

      // Reset tracking
      interactionStartTime = 0;
    }

    // Touch event handling
    function handleTouchStart(event: TouchEvent) {
      isTouch = true;
      if (event.touches.length === 1) {
        const touch = event.touches[0];
        // Record time and position when touch starts
        interactionStartTime = Date.now();
        interactionStartPosition = { x: touch.clientX, y: touch.clientY };
      }
    }

    function handleTouchEnd(event: TouchEvent) {
      // Ensure we mark this as a touch interaction
      isTouch = true;

      // Only process if we have a valid touch start time
      if (interactionStartTime > 0 && event.changedTouches.length === 1) {
        const touchEndTime = Date.now();
        const touchDuration = touchEndTime - interactionStartTime;

        // Only trigger if touch was quick (like a tap, not a long press or drag)
        if (touchDuration < CLICK_DURATION_THRESHOLD) {
          const touch = event.changedTouches[0];

          // Calculate distance moved to determine if it was a tap or a drag
          const distX = Math.abs(touch.clientX - interactionStartPosition.x);
          const distY = Math.abs(touch.clientY - interactionStartPosition.y);

          // If movement was minimal (user didn't drag)
          if (distX < CLICK_DISTANCE_THRESHOLD && distY < CLICK_DISTANCE_THRESHOLD) {
            const rect = renderer.domElement.getBoundingClientRect();
            const x = ((touch.clientX - rect.left) / rect.width) * 2 - 1;
            const y = -((touch.clientY - rect.top) / rect.height) * 2 + 1;

            // Pass to HeartScene for raycasting and detection
            torusScene.handleMouseClick(x, y, camera);
          }
        }

        // Reset touch tracking
        interactionStartTime = 0;
      }
    }

    // Add event listeners - using mouseup/mousedown instead of click
    renderer.domElement.addEventListener('mousedown', handleMouseDown);
    renderer.domElement.addEventListener('mouseup', handleMouseUp);
    renderer.domElement.addEventListener('touchstart', handleTouchStart);
    renderer.domElement.addEventListener('touchend', handleTouchEnd);

    // Handle resizing
    function onResize() {
      if (!mountRef.current) return;

      const w = mountRef.current.clientWidth;
      const h = mountRef.current.clientHeight;

      camera.aspect = w / h;
      camera.updateProjectionMatrix();
      renderer.setSize(w, h);
    }

    window.addEventListener('resize', onResize);

    // Call resize once to ensure proper initial sizing
    onResize();

    // Animation loop
    renderer.setAnimationLoop(() => {
      // Update the scene for a more dynamic appearance
      torusScene.update();
      
      // Render the scene
      renderer.setRenderTarget(null);
      renderer.render(torusScene.scene, camera);
      
      controls.update();
      stats.update();
    });

    const mountElement = mountRef.current;

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', onResize);

      // Remove event listeners
      renderer.domElement.removeEventListener('mousedown', handleMouseDown);
      renderer.domElement.removeEventListener('mouseup', handleMouseUp);
      renderer.domElement.removeEventListener('touchstart', handleTouchStart);
      renderer.domElement.removeEventListener('touchend', handleTouchEnd);

      // Dispose of all resources
      renderer.dispose();
      torusScene.dispose();

      if (mountElement) {
        mountElement.innerHTML = '';
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [count, dispatch]);

  return <div ref={mountRef} style={{ width: '100%', height: '100%' }} />;
};

export default HeartsClusterScene;
