import type { NextConfig } from "next";
import withPWA from "next-pwa";

// Configure PWA
const withPWAConfig = withPWA({
  dest: "public",
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === "development",
});

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    // Dangerously allow svg
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
        pathname: "/**",
      },
    ],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none';",
  },
  env: {
    BACKEND_API_URL: process.env.BACKEND_API_URL,
  },
};

// Apply PWA configuration
export default withPWAConfig(nextConfig);
