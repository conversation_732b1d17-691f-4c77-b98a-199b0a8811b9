# Novan Progressive Web App Implementation

This document outlines the implementation of Progressive Web App (PWA) capabilities in the Novan web application.

## Implemented Features

1. **Web App Manifest** (`/public/manifest.json`)
   - Provides metadata for app installation
   - Sets `/dashboard` as the start URL
   - Includes app name, short name, description, and display mode
   - References icon files for various sizes
   - Includes screenshots for desktop and mobile views
   - Defines shortcuts for quick access

2. **Service Worker** (`/public/sw.js`)
   - Enables offline capabilities
   - Caches static assets
   - Handles network requests
   - Implements advanced caching strategies
   - Provides offline fallback page
   - Updates cache version automatically

3. **Service Worker Registration** (`/public/sw-register.js`)
   - Automatically registers the service worker
   - Includes version parameter for cache busting

4. **Next.js PWA Configuration**
   - Added next-pwa integration
   - Updated Next.js configuration for optimal PWA support
   - Added middleware exceptions for PWA assets

5. **PWA Meta Tags**
   - Added appropriate meta tags for iOS and Android
   - Set proper theme colors and viewport settings

6. **Installation Experience**
   - Custom installation prompt component
   - User-friendly install button
   - Remembers user preferences

7. **Offline Support**
   - Custom offline fallback page
   - Graceful degradation of features
   - Cached critical resources

8. **Helper Utilities**
   - `usePWA` hook for detecting installation status
   - PWA version updater script

## Required Actions

Before deploying the PWA, you must:

1. **Replace Icon Placeholders**
   - Generate proper icons based on your branding
   - Run `npm run generate:svg-icons` to create SVG placeholders
   - Convert SVGs to PNGs using an online converter tool
   - Replace the placeholder files in `/public/icons/`
   - Use the heart cluster SVG as the basis for your icon

2. **Add Screenshots**
   - Create actual screenshots of your application
   - Desktop view (1280x720) saved as `/public/screenshots/dashboard.png`
   - Mobile view (720x1280) saved as `/public/screenshots/mobile.png`

3. **Update PWA Version Before Deployment**
   - Run `npm run pwa:update-version` to update the cache version
   - Or use `npm run build:pwa` for a full build with version update

4. **Test the PWA**
   - Verify installability on desktop and mobile browsers
   - Test offline functionality with network disconnection
   - Ensure the offline fallback page works properly
   - Check that the start URL (`/dashboard`) works correctly
   - Test the installation prompt

## Testing PWA Functionality

1. **Lighthouse Audit**
   - Run a Lighthouse audit in Chrome DevTools
   - Check the "PWA" category
   - Address any issues identified by the audit

2. **Installation Testing**
   - Test on Chrome, Edge, Firefox (desktop)
   - Test on Chrome, Safari (iOS), Chrome (Android)
   - Verify the install prompt appears
   - Check the app icon and name after installation

3. **Offline Testing**
   - After installing, disconnect from the internet
   - Launch the PWA and verify it loads
   - Test navigation to cached pages

## References

- [Next.js PWA Documentation](https://nextjs.org/docs)
- [MDN PWA Guide](https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps)
- [Web App Manifest Specification](https://developer.mozilla.org/en-US/docs/Web/Manifest)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)

## Package Dependencies

- `next-pwa`: For Next.js integration with service worker generation
