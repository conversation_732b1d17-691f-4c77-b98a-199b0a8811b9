import { NextRequest, NextResponse } from 'next/server';

// Define which routes require authentication
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/profile/:path*',
    // Exclude PWA assets from authentication requirements
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|sw.js|workbox-*|icons|screenshots).*)',
  ],
};

export function middleware(request: NextRequest) {
  // Get the authentication token from cookies
  const token = request.cookies.get('authToken')?.value;
  const { pathname } = request.nextUrl;

  // Check if the request is for a PWA resource
  const isPWAResource = [
    '/manifest.json',
    '/sw.js',
    '/sw-register.js',
  ].some(path => pathname.startsWith(path));

  // Allow access to PWA resources without authentication
  if (isPWAResource) {
    return NextResponse.next();
  }
  
  // If user is not logged in and trying to access protected routes, redirect to login
  if (!token && (pathname.startsWith('/dashboard') || pathname.startsWith('/profile'))) {
    const url = new URL('/login', request.url);
    url.searchParams.set('callbackUrl', encodeURI(pathname));
    return NextResponse.redirect(url);
  }

  // Allow access to requested page if authenticated or not protected
  return NextResponse.next();
}