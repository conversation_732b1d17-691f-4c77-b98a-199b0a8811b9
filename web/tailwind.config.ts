const config = {
  darkMode: 'class',
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        // Basic theme colors
        background: 'var(--color-background)',
        foreground: 'var(--color-foreground)',
        'foreground-secondary': 'var(--color-foreground-secondary)',
        card: 'var(--color-card)',
        'card-border': 'var(--color-card-border)',
        input: 'var(--color-input)',
        'input-border': 'var(--color-input-border)',
        ring: 'var(--color-ring)',
        
        // Primary palette
        primary: 'var(--color-primary)',
        'primary-foreground': 'var(--color-primary-foreground)',
        secondary: 'var(--color-secondary)',
        accent: 'var(--color-accent)',
        'accent-secondary': 'var(--color-accent-secondary)',
        
        // Direct color access
        'deep-indigo': 'var(--deep-indigo)',
        'soft-lavender': 'var(--soft-lavender)',
        'sunset-coral': 'var(--sunset-coral)',
        'mint-green': 'var(--mint-green)',
      },
      fontFamily: {
        sans: 'var(--font-body)',
        heading: 'var(--font-heading)',
        accent: 'var(--font-accent)',
        mono: 'var(--font-mono)',
      },
      borderRadius: {
        'soft': '12px',
        'softer': '16px',
      },
      boxShadow: {
        'floating': '0 10px 30px -5px rgba(0, 0, 0, 0.1)',
        'floating-dark': '0 10px 30px -5px rgba(0, 0, 0, 0.3)',
      },
    },
  },
  plugins: [],
};

export default config;